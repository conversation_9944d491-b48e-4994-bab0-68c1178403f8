package theme

import (
	config2 "github.com/qoder-ai/qoder-cli/cmd/config"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/tui/config"
)

func InitTheme(cfg *config.TuiConfig) {
	if cfg == nil || cfg.Theme == "" {
		return // Use default theme
	}

	// Set up the theme callback for TUI
	SetUpdateCallback(func(themeName string) error {

		return config2.UpdateTheme(themeName)
	})

	// Try to set the theme from config
	err := SetTheme(cfg.Theme)
	if err != nil {
		logging.Warn("Failed to set theme from config, using default theme", "theme", cfg.Theme, "error", err)
	} else {
		logging.Debug("Set theme from config", "theme", cfg.Theme)
	}
}
