package dialog

import (
	"fmt"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/utils"
	"github.com/qoder-ai/qoder-cli/tui/util"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

const (
	UserCommandPrefix    = "user:"
	ProjectCommandPrefix = "project:"
)

var namedArgPattern = regexp.MustCompile(`\$([A-Z][A-Z0-9_]*)`)

func LoadCustomCommands() ([]Command, error) {
	var commands []Command

	homeCommandsDir := filepath.Join(utils.GetGlobalStorageDir(), "commands")
	homeCommands, err := loadCommandsFromDir(homeCommandsDir, UserCommandPrefix)
	if err != nil {
		logging.Error("Warning: failed to load home commands", "err", err)
	} else {
		commands = append(commands, homeCommands...)
	}

	projectCommandsDir := filepath.Join(".qoder-cli", "commands")
	projectCommands, err := loadCommandsFromDir(projectCommandsDir, ProjectCommandPrefix)
	if err != nil {
		logging.Error("Warning: failed to load project commands", "err", err)
	} else {
		commands = append(commands, projectCommands...)
	}

	return commands, nil
}

func loadCommandsFromDir(commandsDir string, prefix string) ([]Command, error) {
	if _, err := os.Stat(commandsDir); os.IsNotExist(err) {
		return []Command{}, nil
	}

	var commands []Command

	err := filepath.Walk(commandsDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}
		if !strings.HasSuffix(strings.ToLower(info.Name()), ".md") {
			return nil
		}

		content, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("failed to read command file %s: %w", path, err)
		}

		commandId := strings.TrimSuffix(info.Name(), filepath.Ext(info.Name()))

		relPath, err := filepath.Rel(commandsDir, path)
		if err != nil {
			return fmt.Errorf("failed to get relative path for %s: %w", path, err)
		}

		commandIdPath := strings.ReplaceAll(filepath.Dir(relPath), string(filepath.Separator), ":")
		if commandIdPath != "." {
			commandId = commandIdPath + ":" + commandId
		}

		command := Command{
			Id:          prefix + commandId,
			Title:       prefix + commandId,
			Description: fmt.Sprintf("Custom command from %s", relPath),
			Handler: func(cmd Command) tea.Cmd {
				commandContent := string(content)

				matches := namedArgPattern.FindAllStringSubmatch(commandContent, -1)
				if len(matches) > 0 {
					argNames := make([]string, 0)
					argMap := make(map[string]bool)

					for _, match := range matches {
						argName := match[1] // Group 1 is the name without $
						if !argMap[argName] {
							argMap[argName] = true
							argNames = append(argNames, argName)
						}
					}

					// Show multi-arguments dialog for all named arguments
					return util.CmdHandler(ShowMultiArgumentsDialogMsg{
						CommandId: cmd.Id,
						Content:   commandContent,
						ArgNames:  argNames,
					})
				}

				// No arguments needed, run command directly
				return util.CmdHandler(CommandRunCustomMsg{
					Content: commandContent,
					Args:    nil, // No arguments
				})
			},
		}

		commands = append(commands, command)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to load custom commands from %s: %w", commandsDir, err)
	}

	return commands, nil
}

type CommandRunCustomMsg struct {
	Content string
	Args    map[string]string // Map of argument names to values
}
