package config

import (
	cmdCfg "github.com/qoder-ai/qoder-cli/cmd/config"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
)

var storedConfig *TuiConfig
var workingDir string

func WorkingDirectory() string {
	if storedConfig == nil {
		panic("config not loaded")
	}
	return workingDir
}

type TuiConfig struct {
	Theme           string
	CurrentModel    models.ModelId
	AgentCoder      config.AgentName
	AutoCompact     bool
	AvailableModels map[models.ModelId]models.Model
	Providers       map[models.ModelProvider]config.ProviderConfig
	LspConfigs      map[string]config.LspConfig

	ShouldShowInit func() (bool, error)
	ThemeCallback  func(string) error
	InitCallback   func() error
}

type projectInitializer interface {
	Initialized() bool
	Initialize() error
}

func NewTuiConfig(
	cfg *config.Config,
	tuiCfg cmdCfg.TuiConfig,
	themeUpdateCallback func(string) error,
	initializer projectInitializer,
	wd string) *TuiConfig {

	availableModels := make(map[models.ModelId]models.Model)
	for _, agentCfg := range cfg.Agents {
		if model, exists := models.SupportedModels[agentCfg.Model]; exists {
			availableModels[agentCfg.Model] = model
		}
	}

	tuiConfig := &TuiConfig{
		Theme:           tuiCfg.Theme,
		CurrentModel:    cfg.Agents[config.AgentCoder].Model,
		AgentCoder:      config.AgentCoder,
		AutoCompact:     cfg.AutoCompact,
		AvailableModels: availableModels,
		Providers:       cfg.Providers,
		LspConfigs:      cfg.LspConfigs,
		ThemeCallback:   themeUpdateCallback,
		InitCallback:    initializer.Initialize,
		ShouldShowInit: func() (bool, error) {
			return !initializer.Initialized(), nil
		},
	}

	workingDir = wd
	storedConfig = tuiConfig
	return tuiConfig
}
