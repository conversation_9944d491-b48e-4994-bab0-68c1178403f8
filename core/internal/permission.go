package internal

import (
	"path/filepath"
	"slices"
	"sync"

	"github.com/google/uuid"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
)

type permissionService struct {
	*pubsub.Broker[core.PermissionRequest]
	sessionPermissions  []core.PermissionRequest
	pendingRequests     sync.Map
	autoApproveSessions []string
}

func (s *permissionService) GrantPersistent(request core.PermissionRequest) {
	respCh, ok := s.pendingRequests.Load(request.Id)
	if ok {
		respCh.(chan bool) <- true
	}
	s.sessionPermissions = append(s.sessionPermissions, request)
}

func (s *permissionService) Grant(request core.PermissionRequest) {
	respCh, ok := s.pendingRequests.Load(request.Id)
	if ok {
		respCh.(chan bool) <- true
	}
}

func (s *permissionService) Deny(request core.PermissionRequest) {
	respCh, ok := s.pendingRequests.Load(request.Id)
	if ok {
		respCh.(chan bool) <- false
	}
}

func (s *permissionService) CreateRequestWithContext(ctx core.SessionContext, opts core.CreatePermissionRequest) bool {
	if slices.Contains(s.autoApproveSessions, opts.SessionId) {
		return true
	}
	dir := filepath.Dir(opts.Path)
	if dir == "." && ctx != nil {
		dir = ctx.GetWorkingDir()
	}

	request := core.PermissionRequest{
		Id:          uuid.New().String(),
		Path:        dir,
		SessionId:   opts.SessionId,
		ToolName:    opts.ToolName,
		Description: opts.Description,
		Action:      opts.Action,
		Params:      opts.Params,
	}

	for _, p := range s.sessionPermissions {
		if p.ToolName == request.ToolName && p.Action == request.Action && p.SessionId == request.SessionId && p.Path == request.Path {
			return true
		}
	}

	respCh := make(chan bool, 1)

	s.pendingRequests.Store(request.Id, respCh)
	defer s.pendingRequests.Delete(request.Id)

	s.Publish(pubsub.CreatedEvent, request)

	resp := <-respCh
	return resp
}

func (s *permissionService) CreateRequest(opts core.CreatePermissionRequest) bool {
	return s.CreateRequestWithContext(nil, opts)
}

func (s *permissionService) AutoApproveSession(sessionId string) {
	s.autoApproveSessions = append(s.autoApproveSessions, sessionId)
}

func NewPermissionService() core.PermissionService {
	return &permissionService{
		Broker:             pubsub.NewBroker[core.PermissionRequest](),
		sessionPermissions: make([]core.PermissionRequest, 0),
	}
}
