package internal

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/internal/storage"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
)

type messageService struct {
	*pubsub.Broker[message.Message]
	cache      *storage.Cache
	storageSvc *storage.Service
}

func NewMessageService(projSvc core.ProjectService) message.MessageService {
	storageSvc := storage.NewStorageService(projSvc)
	return &messageService{
		Broker:     pubsub.NewBroker[message.Message](),
		cache:      storage.NewCache(),
		storageSvc: storageSvc,
	}
}

func (s *messageService) Create(ctx context.Context, sessionId string, params message.CreateMessageParams) (message.Message, error) {
	ts := time.Now().UnixMilli()

	if params.Role != message.Assistant {
		params.Parts = append(params.Parts, message.Finish{
			Reason: "stop",
			Time:   ts,
		})
	}

	msg := message.Message{
		Id:        uuid.New().String(),
		SessionId: sessionId,
		Role:      params.Role,
		Parts:     params.Parts,
		Model:     params.Model,
		CreatedAt: ts,
		UpdatedAt: ts,
	}

	s.cache.PutMessage(msg)

	fp := params.FinishedPart()
	if fp != nil && fp.Time != 0 {
		if err := s.storageSvc.PersistentMessage(ctx, msg, fp.Time); err != nil {
			return message.Message{}, err
		}
	}

	s.Publish(pubsub.CreatedEvent, msg)
	return msg, nil
}

func (s *messageService) Update(ctx context.Context, msg message.Message) error {
	msg.UpdatedAt = time.Now().UnixMilli()
	s.cache.UpdateMessage(msg)

	fp := msg.FinishPart()
	if fp != nil && fp.Time != 0 {
		if err := s.storageSvc.PersistentMessage(ctx, msg, fp.Time); err != nil {
			return err
		}
	}

	s.Publish(pubsub.UpdatedEvent, msg)
	return nil
}

func (s *messageService) List(ctx context.Context, sessionId string) ([]message.Message, error) {
	messages := s.cache.ListMessages(sessionId)

	if len(messages) != 0 {
		return messages, nil
	}

	var err error
	messages, err = s.storageSvc.LoadSessionMessages(ctx, sessionId)
	if err != nil {
		return nil, err
	}

	s.cache.InitSessionMessages(sessionId, messages)

	return messages, nil
}
