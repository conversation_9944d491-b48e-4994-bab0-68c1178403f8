package internal

import (
	"context"
	"github.com/google/uuid"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/internal/storage"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"time"
)

type sessionService struct {
	*pubsub.Broker[core.Session]
	storageSvc    *storage.Service
	cache         *storage.Cache
	sessionLoaded bool
}

func NewSessionService(projSvc core.ProjectService) core.SessionService {
	storageSvc := storage.NewStorageService(projSvc)

	return &sessionService{
		Broker:     pubsub.NewBroker[core.Session](),
		cache:      storage.NewCache(),
		storageSvc: storageSvc,
	}
}

func (s *sessionService) CreateWithConfig(ctx context.Context, title string, config *core.SessionConfig) (core.Session, error) {
	ts := time.Now().UnixMilli()
	ses := core.Session{
		Id:        uuid.New().String(),
		Title:     title,
		CreatedAt: ts,
		UpdatedAt: ts,
	}

	if config != nil {
		ses.WorkingDir = config.WorkingDir
		ses.ContextPaths = config.ContextPaths
	}

	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return ses, err
	}

	s.cache.PutSession(ses)
	s.Publish(pubsub.CreatedEvent, ses)
	return ses, nil
}

func (s *sessionService) CreateTaskSession(ctx context.Context, toolCallId, parentId, title string) (core.Session, error) {
	parent, err := s.Get(ctx, parentId)
	if err != nil {
		return core.Session{}, err
	}

	ts := time.Now().UnixMilli()
	ses := core.Session{
		Id:              toolCallId,
		ParentSessionId: parentId,
		Title:           title,
		WorkingDir:      parent.WorkingDir,
		ContextPaths:    parent.ContextPaths,
		CreatedAt:       ts,
		UpdatedAt:       ts,
	}

	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return ses, err
	}

	s.cache.PutSession(ses)
	s.Publish(pubsub.CreatedEvent, ses)
	return ses, nil
}

func (s *sessionService) Get(ctx context.Context, id string) (core.Session, error) {
	ses := s.cache.GetSession(id)
	if ses != nil {
		return *ses, nil
	}

	ses, err := s.storageSvc.LoadSession(ctx, id)

	if err != nil {
		return core.Session{}, err
	}

	s.cache.PutSession(*ses)
	return *ses, nil
}

func (s *sessionService) Save(ctx context.Context, ses core.Session) error {
	s.cache.UpdateSession(ses)
	if err := s.storageSvc.PersistentSession(ctx, ses); err != nil {
		return err
	}
	s.Publish(pubsub.UpdatedEvent, ses)
	return nil
}

func (s *sessionService) List(ctx context.Context) ([]core.Session, error) {
	if s.sessionLoaded {
		return s.cache.ListSessions(), nil
	}

	sessions, err := s.storageSvc.LoadAllSessions(ctx)
	if err != nil {
		return nil, err
	}

	s.cache.InitSessions(sessions)
	s.sessionLoaded = true
	return sessions, nil
}
