package storage

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/message"
	"os"
	"path/filepath"
	"sync"
)

type partType string

const (
	reasoningType  partType = "reasoning"
	textType       partType = "text"
	imageURLType   partType = "image_url"
	binaryType     partType = "binary"
	toolCallType   partType = "tool_call"
	toolResultType partType = "tool_result"
	finishType     partType = "finish"
)

type partWrapper struct {
	Type partType            `json:"type"`
	Data message.ContentPart `json:"data"`
}

type storedMessage struct {
	Id         string `json:"id"`
	SessionId  string `json:"session_id"`
	Role       string `json:"role"`
	Parts      string `json:"parts"`
	Model      string `json:"model"`
	CreatedAt  int64  `json:"created_at"`
	UpdatedAt  int64  `json:"updated_at"`
	FinishedAt int64  `json:"finished_at"`
}

func (s storedMessage) toMessage() (message.Message, error) {
	parts, err := unmarshallParts([]byte(s.Parts))
	if err != nil {
		return message.Message{}, err
	}
	return message.Message{
		Id:        s.Id,
		SessionId: s.SessionId,
		Role:      message.Role(s.Role),
		Parts:     parts,
		Model:     models.ModelId(s.Model),
		CreatedAt: s.CreatedAt,
		UpdatedAt: s.UpdatedAt,
	}, nil
}

type UpdateMessageParams struct {
	Parts      string `json:"parts"`
	FinishedAt int64  `json:"finished_at"`
	ID         string `json:"id"`
}

type Service struct {
	projSvc core.ProjectService
	msgLock sync.RWMutex
	sesLock sync.RWMutex
}

func NewStorageService(projSvc core.ProjectService) *Service {
	return &Service{projSvc: projSvc}
}

func (s *Service) getSessionMessagesFile(sessionId string) string {
	return filepath.Join(s.projSvc.GetStorageDir(), sessionId+".jsonl")
}

func (s *Service) LoadSessionMessages(ctx context.Context, sessionId string) ([]message.Message, error) {
	s.msgLock.RLock()
	defer s.msgLock.RUnlock()

	if !s.projSvc.StorageDirExists() {
		return []message.Message{}, nil
	}

	f := s.getSessionMessagesFile(sessionId)
	if !fileExists(f) {
		return []message.Message{}, nil
	}

	return readMessagesFromFile(f)
}

func (s *Service) PersistentMessage(ctx context.Context, msg message.Message, finishedAt int64) error {
	parts, err := marshallParts(msg.Parts)
	if err != nil {
		return err
	}

	sm := storedMessage{
		Id:         msg.Id,
		SessionId:  msg.SessionId,
		Role:       string(msg.Role),
		Parts:      string(parts),
		Model:      string(msg.Model),
		CreatedAt:  msg.CreatedAt,
		UpdatedAt:  msg.UpdatedAt,
		FinishedAt: finishedAt,
	}

	s.msgLock.Lock()
	defer s.msgLock.Unlock()

	s.projSvc.EnsureStorageDir()
	return appendMessagesToFile(s.getSessionMessagesFile(msg.SessionId), []storedMessage{sm})
}

func readMessagesFromFile(filename string) ([]message.Message, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var messages []message.Message
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		var sm storedMessage
		if err := json.Unmarshal(scanner.Bytes(), &sm); err != nil {
			return nil, err
		}

		msg, err := sm.toMessage()
		if err != nil {
			return nil, err
		}

		messages = append(messages, msg)
	}

	if err = scanner.Err(); err != nil {
		return nil, err
	}

	return messages, nil
}

func appendMessagesToFile(filename string, messages []storedMessage) error {
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	for _, msg := range messages {
		data, err := json.Marshal(msg)
		if err != nil {
			return err
		}

		if _, err := writer.Write(data); err != nil {
			return err
		}
		if err := writer.WriteByte('\n'); err != nil {
			return err
		}
	}

	return nil
}

func marshallParts(parts []message.ContentPart) ([]byte, error) {
	wrappedParts := make([]partWrapper, len(parts))

	for i, part := range parts {
		var typ partType

		switch part.(type) {
		case message.ReasoningContent:
			typ = reasoningType
		case message.TextContent:
			typ = textType
		case message.ImageUrlContent:
			typ = imageURLType
		case message.BinaryContent:
			typ = binaryType
		case message.ToolCall:
			typ = toolCallType
		case message.ToolResult:
			typ = toolResultType
		case message.Finish:
			typ = finishType
		default:
			return nil, fmt.Errorf("unknown part type: %T", part)
		}

		wrappedParts[i] = partWrapper{
			Type: typ,
			Data: part,
		}
	}
	return json.Marshal(wrappedParts)
}

func unmarshallParts(data []byte) ([]message.ContentPart, error) {
	temp := []json.RawMessage{}

	if err := json.Unmarshal(data, &temp); err != nil {
		return nil, err
	}

	parts := make([]message.ContentPart, 0)

	for _, rawPart := range temp {
		var wrapper struct {
			Type partType        `json:"type"`
			Data json.RawMessage `json:"data"`
		}

		if err := json.Unmarshal(rawPart, &wrapper); err != nil {
			return nil, err
		}

		switch wrapper.Type {
		case reasoningType:
			part := message.ReasoningContent{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
			parts = append(parts, part)
		case textType:
			part := message.TextContent{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
			parts = append(parts, part)
		case imageURLType:
			part := message.ImageUrlContent{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
		case binaryType:
			part := message.BinaryContent{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
			parts = append(parts, part)
		case toolCallType:
			part := message.ToolCall{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
			parts = append(parts, part)
		case toolResultType:
			part := message.ToolResult{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
			parts = append(parts, part)
		case finishType:
			part := message.Finish{}
			if err := json.Unmarshal(wrapper.Data, &part); err != nil {
				return nil, err
			}
			parts = append(parts, part)
		default:
			return nil, fmt.Errorf("unknown part type: %s", wrapper.Type)
		}

	}

	return parts, nil
}
