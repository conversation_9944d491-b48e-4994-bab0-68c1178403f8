package storage

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core"
	"os"
	"path/filepath"
	"sort"
)

func (s *Service) getSessionFilePath(sessionId string) string {
	return filepath.Join(s.projSvc.GetStorageDir(), sessionId)
}

func (s *Service) ListSessionModifiedFiles(ctx context.Context, sessionId string) ([]core.File, error) {
	fp := s.getSessionFilePath(sessionId)

	if !fileExists(fp) {
		return []core.File{}, nil
	}

	matches, err := filepath.Glob(filepath.Join(fp, "*.json"))
	if err != nil {
		return nil, err
	}

	sort.Sort(sort.Reverse(sort.StringSlice(matches)))

	var files []core.File
	for i := range matches {
		file, err := loadFile(matches[i])
		if err != nil {
			return nil, err
		}

		files = append(files, *file)
	}

	return files, nil
}

func (s *Service) CreateFileVersion(ctx context.Context, file core.File) error {
	fp := s.getSessionFilePath(file.SessionId)

	if !fileExists(fp) {
		if err := os.MkdirAll(fp, 0755); err != nil {
			return err
		}
	}

	return writeFile(fp, file)
}

func (s *Service) GetLatestVersion(ctx context.Context, sessionId, path string) (*core.File, error) {
	fp := s.getSessionFilePath(sessionId)

	if !fileExists(fp) {
		return nil, nil
	}

	matches, err := filepath.Glob(filepath.Join(fp, encodeFilePath(path)+"-*.json"))

	if err != nil {
		return nil, err
	}

	sort.Strings(matches)

	if len(matches) == 0 {
		return nil, nil
	}

	return loadFile(matches[len(matches)-1])
}

func (s *Service) ListLatestVersion(ctx context.Context, sessionId string) ([]core.File, error) {
	files, err := s.ListSessionModifiedFiles(ctx, sessionId)
	if err != nil {
		return nil, err
	}

	fmap := make(map[string]bool)

	for i, f := range files {
		if !fmap[f.Path] {
			fmap[f.Path] = true
			files = append(files, files[i])
		}
	}

	return files, nil
}

func encodeFilePath(path string) string {
	hash := md5.Sum([]byte(path))
	return hex.EncodeToString(hash[:])
}

func getStoredFileName(originPath, version string) string {
	return encodeFilePath(originPath) + "-" + version + ".json"
}

func loadFile(filename string) (*core.File, error) {
	contents, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	file := &core.File{}
	if err = json.Unmarshal(contents, file); err != nil {
		return nil, err
	}

	return file, nil
}

func writeFile(path string, file core.File) error {
	contents, err := json.Marshal(file)
	if err != nil {
		return err
	}

	f := filepath.Join(path, getStoredFileName(file.Path, file.Version))

	return os.WriteFile(f, contents, 0644)
}
