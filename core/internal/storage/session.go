package storage

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core"
	"os"
	"path/filepath"
)

func (s *Service) LoadAllSessions(ctx context.Context) ([]core.Session, error) {
	s.sesLock.RLock()
	defer s.sesLock.RUnlock()

	if !s.projSvc.StorageDirExists() {
		return nil, nil
	}

	matches, err := filepath.Glob(filepath.Join(s.projSvc.GetStorageDir(), "*-session.json"))
	if err != nil {
		return nil, err
	}

	var sessions []core.Session
	for _, match := range matches {
		ses, err := loadSessionFromFile(match)
		if err != nil {
			return nil, err
		}
		if ses != nil {
			sessions = append(sessions, *ses)
		}
	}
	return sessions, nil
}

func (s *Service) LoadSession(ctx context.Context, sessionId string) (*core.Session, error) {
	s.sesLock.RLock()
	defer s.sesLock.RUnlock()

	if !s.projSvc.StorageDirExists() {
		return nil, nil
	}

	return loadSessionFromFile(filepath.Join(s.projSvc.GetStorageDir(), sessionId+"-session.json"))
}

func (s *Service) PersistentSession(ctx context.Context, ses core.Session) error {
	s.msgLock.Lock()
	defer s.msgLock.Unlock()
	s.projSvc.EnsureStorageDir()

	content, err := json.Marshal(ses)
	if err != nil {
		return err
	}

	filename := filepath.Join(s.projSvc.GetStorageDir(), ses.Id+"-session.json")
	return os.WriteFile(filename, content, 0644)
}

func loadSessionFromFile(filename string) (*core.Session, error) {
	contents, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	ses := &core.Session{}
	if err = json.Unmarshal(contents, ses); err != nil {
		return nil, err
	}

	return ses, nil
}
