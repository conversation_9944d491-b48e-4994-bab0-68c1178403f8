package agent

import (
	"context"
	"errors"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/internal/llm/prompt"
	"github.com/qoder-ai/qoder-cli/core/llm/agent"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/llm/provider"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"strings"
	"sync"
	"time"
)

type agentRunner struct {
	*pubsub.Broker[agent.Event]
	messages  message.MessageService
	sessions  core.SessionService
	providers provider.Service
	tools     []tools.BaseTool
	config    *config.Config

	client          provider.Client
	titleClient     provider.Client
	summarizeClient provider.Client
	activeRequests  sync.Map
}

func NewAgent(agentName config.AgentName, app app.App, agentTools []tools.BaseTool) (agent.Agent, error) {
	cfg := app.GetConfig()
	client, err := createClientWithConfig(app.GetProviderService(), agentName, cfg)
	if err != nil {
		return nil, err
	}
	var titleProvider provider.Client
	if agentName == config.AgentCoder {
		titleProvider, err = createClientWithConfig(app.GetProviderService(), config.AgentTitle, cfg)
		if err != nil {
			return nil, err
		}
	}
	var summarizeProvider provider.Client
	if agentName == config.AgentCoder {
		summarizeProvider, err = createClientWithConfig(app.GetProviderService(), config.AgentSummarizer, cfg)
		if err != nil {
			return nil, err
		}
	}

	a := &agentRunner{
		Broker:          pubsub.NewBroker[agent.Event](),
		client:          client,
		messages:        app.GetMessageService(),
		providers:       app.GetProviderService(),
		sessions:        app.GetSessionService(),
		tools:           agentTools,
		titleClient:     titleProvider,
		summarizeClient: summarizeProvider,
		activeRequests:  sync.Map{},
		config:          cfg,
	}

	return a, nil
}

func (a *agentRunner) Model() models.Model {
	return a.client.GetModel()
}

func (a *agentRunner) Cancel(sessionID string) {
	// Cancel regular requests
	if cancelFunc, exists := a.activeRequests.LoadAndDelete(sessionID); exists {
		if cancel, ok := cancelFunc.(context.CancelFunc); ok {
			logging.InfoPersist(fmt.Sprintf("Request cancellation initiated for session: %s", sessionID))
			cancel()
		}
	}

	// Also check for summarize requests
	if cancelFunc, exists := a.activeRequests.LoadAndDelete(sessionID + "-summarize"); exists {
		if cancel, ok := cancelFunc.(context.CancelFunc); ok {
			logging.InfoPersist(fmt.Sprintf("Summarize cancellation initiated for session: %s", sessionID))
			cancel()
		}
	}
}

func (a *agentRunner) IsBusy() bool {
	busy := false
	a.activeRequests.Range(func(key, value interface{}) bool {
		if cancelFunc, ok := value.(context.CancelFunc); ok {
			if cancelFunc != nil {
				busy = true
				return false // Stop iterating
			}
		}
		return true // Continue iterating
	})
	return busy
}

func (a *agentRunner) IsSessionBusy(sessionID string) bool {
	_, busy := a.activeRequests.Load(sessionID)
	return busy
}

func (a *agentRunner) generateTitle(ctx context.Context, sessionID string, content string) error {
	if content == "" {
		return nil
	}
	if a.titleClient == nil {
		return nil
	}

	ses, err := a.sessions.Get(ctx, sessionID)
	if err != nil {
		return err
	}

	ctx = context.WithValue(ctx, tools.SessionIDContextKey, sessionID)
	parts := []message.ContentPart{message.TextContent{Text: content, Type: "text"}}
	response, err := a.titleClient.SendMessages(
		ctx,
		[]message.Message{
			{
				Role:  message.User,
				Parts: parts,
			},
		},
		make([]tools.BaseTool, 0),
	)
	if err != nil {
		return err
	}

	if response == nil {
		return errors.New("response is nil")
	}

	title := strings.TrimSpace(strings.ReplaceAll(response.Content, "\n", " "))
	if title == "" {
		return nil
	}

	ses.Title = title
	return a.sessions.Save(ctx, ses)
}

func (a *agentRunner) err(err error) agent.Event {
	return agent.Event{
		Type:  agent.AgentEventTypeError,
		Error: err,
	}
}

func (a *agentRunner) Run(ctx context.Context, sessionID string, content string, attachments ...message.Attachment) (<-chan agent.Event, error) {
	if !a.client.GetModel().SupportsAttachments && attachments != nil {
		attachments = nil
	}
	events := make(chan agent.Event)
	if a.IsSessionBusy(sessionID) {
		return nil, agent.ErrSessionBusy
	}

	genCtx, cancel := context.WithCancel(ctx)

	a.activeRequests.Store(sessionID, cancel)
	go func() {
		logging.Debug("Request started", "sessionID", sessionID)
		defer logging.RecoverPanic("agent.Run", func() {
			events <- a.err(fmt.Errorf("panic while running the agent"))
		})

		var toolNames []string
		for _, tool := range a.tools {
			toolNames = append(toolNames, tool.Info().Name)
		}
		a.Publish(pubsub.CreatedEvent, agent.Event{
			Type:      agent.AgentEventTypeSystem,
			Subtype:   "init",
			SessionID: sessionID,
			Config: &agent.Config{
				Cwd:            a.config.WorkingDir,
				Model:          a.client.GetModel().Id,
				Tools:          toolNames,
				PermissionMode: "bypassPermissions",
			},
		})

		var attachmentParts []message.ContentPart
		for _, attachment := range attachments {
			attachmentParts = append(attachmentParts, message.BinaryContent{Path: attachment.FilePath, MIMEType: attachment.MimeType, Data: attachment.Content})
		}
		result := a.processGeneration(genCtx, sessionID, content, attachmentParts)
		if result.Error != nil && !errors.Is(result.Error, agent.ErrRequestCancelled) && !errors.Is(result.Error, context.Canceled) {
			logging.ErrorPersist(result.Error.Error())
		}
		logging.Debug("Request completed", "sessionID", sessionID)
		a.activeRequests.Delete(sessionID)
		cancel()
		a.Publish(pubsub.CreatedEvent, result)
		events <- result
		close(events)
	}()
	return events, nil
}

func (a *agentRunner) processGeneration(ctx context.Context, sessionID, content string, attachmentParts []message.ContentPart) agent.Event {
	msgs, err := a.messages.List(ctx, sessionID)
	if err != nil {
		return a.err(fmt.Errorf("failed to list messages: %w", err))
	}
	if len(msgs) == 0 {
		go func() {
			defer logging.RecoverPanic("agent.Run", func() {
				logging.ErrorPersist("panic while generating title")
			})
			titleErr := a.generateTitle(context.Background(), sessionID, content)
			if titleErr != nil {
				logging.ErrorPersist(fmt.Sprintf("failed to generate title: %v", titleErr))
			}
		}()
	}

	ses, err := a.sessions.Get(ctx, sessionID)
	if err != nil {
		return a.err(fmt.Errorf("failed to get session: %w", err))
	}
	if ses.SummaryMessageId != "" {
		summaryMsgInex := -1
		for i, msg := range msgs {
			if msg.Id == ses.SummaryMessageId {
				summaryMsgInex = i
				break
			}
		}
		if summaryMsgInex != -1 {
			msgs = msgs[summaryMsgInex:]
			msgs[0].Role = message.User
		}
	}

	userMsg, err := a.createUserMessage(ctx, sessionID, content, attachmentParts)
	if err != nil {
		return a.err(fmt.Errorf("failed to create user message: %w", err))
	}
	msgHistory := append(msgs, userMsg)

	for {
		select {
		case <-ctx.Done():
			return a.err(ctx.Err())
		default:
		}
		agentMessage, toolResults, err := a.streamAndHandleEvents(ctx, sessionID, msgHistory)
		if err != nil {
			if errors.Is(err, context.Canceled) {
				agentMessage.AddFinish(message.FinishReasonCanceled)
				a.messages.Update(context.Background(), agentMessage)
				return a.err(agent.ErrRequestCancelled)
			}
			return a.err(fmt.Errorf("failed to process events: %w", err))
		}

		if a.config.Debug {
			seqId := (len(msgHistory) + 1) / 2
			toolResultFilepath := logging.WriteToolResultsJson(sessionID, seqId, toolResults)
			logging.Info("Result", "message", agentMessage.FinishReason(), "toolResults", "{}", "filepath", toolResultFilepath)
		} else {
			logging.Info("Result", "message", agentMessage.FinishReason(), "toolResults", toolResults)
		}
		if (agentMessage.FinishReason() == message.FinishReasonToolUse) && toolResults != nil {
			// We are not done, we need to respond with the tool response
			msgHistory = append(msgHistory, agentMessage, *toolResults)
			continue
		}

		return agent.Event{
			Type:      agent.AgentEventTypeResult,
			Subtype:   "success",
			Message:   &agentMessage,
			SessionID: sessionID,
			Done:      true,
		}
	}
}

func (a *agentRunner) createUserMessage(ctx context.Context, sessionID, content string, attachmentParts []message.ContentPart) (message.Message, error) {
	parts := []message.ContentPart{message.TextContent{Text: content, Type: "text"}}
	parts = append(parts, attachmentParts...)
	return a.messages.Create(ctx, sessionID, message.CreateMessageParams{
		Role:  message.User,
		Parts: parts,
	})
}

func (a *agentRunner) chatRequest(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (string, error) {
	res, err := a.client.SendMessages(ctx, messages, tools)
	return res.Content, err
}

func (a *agentRunner) streamAndHandleEvents(ctx context.Context, sessionID string, msgHistory []message.Message) (message.Message, *message.Message, error) {
	ctx = context.WithValue(ctx, tools.SessionIDContextKey, sessionID)
	eventChan := a.client.StreamResponse(ctx, msgHistory, a.tools)

	assistantMsg, err := a.messages.Create(ctx, sessionID, message.CreateMessageParams{
		Role:  message.Assistant,
		Parts: []message.ContentPart{},
		Model: a.client.GetModel().Id,
	})
	if err != nil {
		return assistantMsg, nil, fmt.Errorf("failed to create assistant message: %w", err)
	}

	ctx = context.WithValue(ctx, tools.MessageIDContextKey, assistantMsg.Id)

	for event := range eventChan {
		if processErr := a.processEvent(ctx, sessionID, &assistantMsg, event); processErr != nil {
			a.finishMessage(ctx, &assistantMsg, message.FinishReasonCanceled)
			return assistantMsg, nil, processErr
		}
		if ctx.Err() != nil {
			a.finishMessage(context.Background(), &assistantMsg, message.FinishReasonCanceled)
			return assistantMsg, nil, ctx.Err()
		}
	}

	toolResults := make([]message.ToolResult, len(assistantMsg.ToolCalls()))
	toolCalls := assistantMsg.ToolCalls()
	for i, toolCall := range toolCalls {
		select {
		case <-ctx.Done():
			a.finishMessage(context.Background(), &assistantMsg, message.FinishReasonCanceled)
			for j := i; j < len(toolCalls); j++ {
				toolResults[j] = message.ToolResult{
					ToolCallId: toolCalls[j].Id,
					Content:    "Tool execution canceled by user",
					IsError:    true,
				}
			}
			goto out
		default:
			var tool tools.BaseTool
			for _, availableTool := range a.tools {
				if availableTool.Info().Name == toolCall.Name {
					tool = availableTool
					break
				}
			}

			// Tool not found
			if tool == nil {
				toolResults[i] = message.ToolResult{
					ToolCallId: toolCall.Id,
					Content:    fmt.Sprintf("Tool not found: %s", toolCall.Name),
					IsError:    true,
				}
				continue
			}

			// Create ToolExecutionContext for the tool
			ses, err := a.sessions.Get(ctx, sessionID)
			if err != nil {
				toolResults[i] = message.ToolResult{
					ToolCallId: toolCall.Id,
					Content:    fmt.Sprintf("Failed to get session: %v", err),
					IsError:    true,
				}
				continue
			}

			toolCtx := tools.NewToolExecutionContextBuilder(ctx).
				WithSessionID(sessionID).
				WithMessageID(assistantMsg.Id).
				WithWorkingDir(ses.WorkingDir).
				WithContextPaths(ses.ContextPaths).
				WithConfig("shell.path", a.config.Shell.Path).
				WithConfig("shell.args", strings.Join(a.config.Shell.Args, " ")).
				Build()

			toolResult, toolErr := tool.Run(toolCtx, tools.ToolCall{
				ChatRequest: a.chatRequest,
				Id:          toolCall.Id,
				Name:        toolCall.Name,
				Input:       toolCall.Input,
			})
			if toolErr != nil {
				if errors.Is(toolErr, core.ErrorPermissionDenied) {
					toolResults[i] = message.ToolResult{
						ToolCallId: toolCall.Id,
						Content:    "Permission denied",
						IsError:    true,
					}
					for j := i + 1; j < len(toolCalls); j++ {
						toolResults[j] = message.ToolResult{
							ToolCallId: toolCalls[j].Id,
							Content:    "Tool execution canceled by user",
							IsError:    true,
						}
					}
					a.finishMessage(ctx, &assistantMsg, message.FinishReasonPermissionDenied)
					break
				}
			}
			toolResults[i] = message.ToolResult{
				ToolCallId: toolCall.Id,
				Content:    toolResult.Content,
				Metadata:   toolResult.Metadata,
				IsError:    toolResult.IsError,
			}
		}
	}
out:
	if len(toolResults) == 0 {
		return assistantMsg, nil, nil
	}
	parts := make([]message.ContentPart, 0)
	for _, tr := range toolResults {
		parts = append(parts, tr)
	}
	msg, err := a.messages.Create(context.Background(), assistantMsg.SessionId, message.CreateMessageParams{
		Role:  message.Tool,
		Parts: parts,
	})
	if err != nil {
		return assistantMsg, nil, fmt.Errorf("failed to create cancelled tool message: %w", err)
	}

	return assistantMsg, &msg, err
}

func (a *agentRunner) finishMessage(ctx context.Context, msg *message.Message, finishReason message.FinishReason) {
	msg.AddFinish(finishReason)
	_ = a.messages.Update(ctx, *msg)
}

func (a *agentRunner) processEvent(ctx context.Context, sessionID string, assistantMsg *message.Message, event provider.Event) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	switch event.Type {
	case provider.EventThinkingDelta:
		assistantMsg.AppendReasoningContent(event.Content)
		return a.messages.Update(ctx, *assistantMsg)
	case provider.EventContentDelta:
		assistantMsg.AppendContent(event.Content)
		return a.messages.Update(ctx, *assistantMsg)
	case provider.EventToolUseStart:
		assistantMsg.AddToolCall(*event.ToolCall)
		return a.messages.Update(ctx, *assistantMsg)
	case provider.EventToolUseStop:
		assistantMsg.FinishToolCall(event.ToolCall.Id)
		return a.messages.Update(ctx, *assistantMsg)
	case provider.EventError:
		if errors.Is(event.Error, context.Canceled) {
			logging.InfoPersist(fmt.Sprintf("Event processing canceled for session: %s", sessionID))
			return context.Canceled
		}
		logging.ErrorPersist(event.Error.Error())
		return event.Error
	case provider.EventComplete:
		assistantMsg.SetToolCalls(event.Response.ToolCalls)
		assistantMsg.AddFinish(event.Response.FinishReason)
		if event.Response.Signature != "" {
			assistantMsg.SetReasoningSignature(event.Response.Signature)
		}
		if err := a.messages.Update(ctx, *assistantMsg); err != nil {
			return fmt.Errorf("failed to update message: %w", err)
		}
		return a.TrackUsage(ctx, sessionID, a.client.GetModel(), event.Response.Usage)
	}

	return nil
}

func (a *agentRunner) TrackUsage(ctx context.Context, sessionID string, model models.Model, usage provider.TokenUsage) error {
	sess, err := a.sessions.Get(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}

	cost := model.CostPer1MInCached/1e6*float64(usage.CacheCreationTokens) +
		model.CostPer1MOutCached/1e6*float64(usage.CacheReadTokens) +
		model.CostPer1MIn/1e6*float64(usage.InputTokens) +
		model.CostPer1MOut/1e6*float64(usage.OutputTokens)

	sess.Cost += cost
	sess.CompletionTokens = usage.OutputTokens + usage.CacheReadTokens
	sess.PromptTokens = usage.InputTokens + usage.CacheCreationTokens

	err = a.sessions.Save(ctx, sess)
	if err != nil {
		return fmt.Errorf("failed to save session: %w", err)
	}
	return nil
}

func (a *agentRunner) Update(agentName config.AgentName, modelID models.ModelId) error {
	if a.IsBusy() {
		return fmt.Errorf("cannot change model while processing requests")
	}

	if err := a.config.TriggerModelUpdate(agentName, modelID); err != nil {
		return fmt.Errorf("failed to update config: %w", err)
	}

	if agentConfig, exists := a.config.Agents[agentName]; exists {
		agentConfig.Model = modelID
		a.config.Agents[agentName] = agentConfig
	}

	client, err := createClientWithConfig(a.providers, agentName, a.config)
	if err != nil {
		return fmt.Errorf("failed to create client for model %s: %w", modelID, err)
	}

	a.client = client

	return nil
}

func (a *agentRunner) Summarize(ctx context.Context, sessionID string) error {
	if a.summarizeClient == nil {
		return fmt.Errorf("summarize client not available")
	}

	if a.IsSessionBusy(sessionID) {
		return agent.ErrSessionBusy
	}

	summarizeCtx, cancel := context.WithCancel(ctx)
	a.activeRequests.Store(sessionID+"-summarize", cancel)

	go func() {
		defer a.activeRequests.Delete(sessionID + "-summarize")
		defer cancel()
		event := agent.Event{
			Type:     agent.AgentEventTypeSummarize,
			Progress: "Starting summarization...",
		}

		a.Publish(pubsub.CreatedEvent, event)
		msgs, err := a.messages.List(summarizeCtx, sessionID)
		if err != nil {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("failed to list messages: %w", err),
				Done:  true,
			}
			a.Publish(pubsub.CreatedEvent, event)
			return
		}
		summarizeCtx = context.WithValue(summarizeCtx, tools.SessionIDContextKey, sessionID)

		if len(msgs) == 0 {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("no messages to summarize"),
				Done:  true,
			}
			a.Publish(pubsub.CreatedEvent, event)
			return
		}

		event = agent.Event{
			Type:     agent.AgentEventTypeSummarize,
			Progress: "Analyzing conversation...",
		}
		a.Publish(pubsub.CreatedEvent, event)
		summarizePrompt := "Provide a detailed but concise summary of our conversation above. Focus on information that would be helpful for continuing the conversation, including what we did, what we're doing, which files we're working on, and what we're going to do next."
		promptMsg := message.Message{
			Role:  message.User,
			Parts: []message.ContentPart{message.TextContent{Text: summarizePrompt, Type: "text"}},
		}

		msgsWithPrompt := append(msgs, promptMsg)

		event = agent.Event{
			Type:     agent.AgentEventTypeSummarize,
			Progress: "Generating summary...",
		}

		a.Publish(pubsub.CreatedEvent, event)

		response, err := a.summarizeClient.SendMessages(
			summarizeCtx,
			msgsWithPrompt,
			make([]tools.BaseTool, 0),
		)
		if err != nil {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("failed to summarize: %w", err),
				Done:  true,
			}
			a.Publish(pubsub.CreatedEvent, event)
			return
		}

		summary := strings.TrimSpace(response.Content)
		if summary == "" {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("empty summary returned"),
				Done:  true,
			}
			a.Publish(pubsub.CreatedEvent, event)
			return
		}
		event = agent.Event{
			Type:     agent.AgentEventTypeSummarize,
			Progress: "Creating new session...",
		}

		a.Publish(pubsub.CreatedEvent, event)
		oldSession, err := a.sessions.Get(summarizeCtx, sessionID)
		if err != nil {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("failed to get session: %w", err),
				Done:  true,
			}

			a.Publish(pubsub.CreatedEvent, event)
			return
		}
		msg, err := a.messages.Create(summarizeCtx, oldSession.Id, message.CreateMessageParams{
			Role: message.Assistant,
			Parts: []message.ContentPart{
				message.TextContent{Text: summary, Type: "text"},
				message.Finish{
					Reason: message.FinishReasonEndTurn,
					Time:   time.Now().UnixMilli(),
				},
			},
			Model: a.summarizeClient.GetModel().Id,
		})
		if err != nil {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("failed to create summary message: %w", err),
				Done:  true,
			}

			a.Publish(pubsub.CreatedEvent, event)
			return
		}
		oldSession.SummaryMessageId = msg.Id
		oldSession.CompletionTokens = response.Usage.OutputTokens
		oldSession.PromptTokens = 0
		model := a.summarizeClient.GetModel()
		usage := response.Usage
		cost := model.CostPer1MInCached/1e6*float64(usage.CacheCreationTokens) +
			model.CostPer1MOutCached/1e6*float64(usage.CacheReadTokens) +
			model.CostPer1MIn/1e6*float64(usage.InputTokens) +
			model.CostPer1MOut/1e6*float64(usage.OutputTokens)
		oldSession.Cost += cost
		err = a.sessions.Save(summarizeCtx, oldSession)
		if err != nil {
			event = agent.Event{
				Type:  agent.AgentEventTypeError,
				Error: fmt.Errorf("failed to save session: %w", err),
				Done:  true,
			}
			a.Publish(pubsub.CreatedEvent, event)
		}

		event = agent.Event{
			Type:      agent.AgentEventTypeSummarize,
			SessionID: oldSession.Id,
			Progress:  "Summary complete",
			Done:      true,
		}
		a.Publish(pubsub.CreatedEvent, event)
	}()

	return nil
}

func createClientWithConfig(providers provider.Service, agentName config.AgentName, cfg *config.Config) (provider.Client, error) {
	agentConfig, ok := cfg.GetAgent(agentName)
	if !ok {
		return nil, fmt.Errorf("agent %s not found", agentName)
	}
	model, ok := models.SupportedModels[agentConfig.Model]
	if !ok {
		return nil, fmt.Errorf("model %s not supported", agentConfig.Model)
	}

	providerCfg, ok := cfg.GetProvider(model.Provider)
	if !ok {
		return nil, fmt.Errorf("client %s not supported", model.Provider)
	}
	if providerCfg.Disabled {
		return nil, fmt.Errorf("client %s is not enabled", model.Provider)
	}
	maxTokens := model.DefaultMaxTokens
	if agentConfig.MaxTokens > 0 {
		maxTokens = agentConfig.MaxTokens
	}
	opts := []provider.ClientOption{
		provider.WithAPIKey(providerCfg.ApiKey),
		provider.WithModel(model),
		provider.WithSystemMessage(prompt.GetAgentPromptWithConfig(agentName, model.Provider, cfg.WorkingDir, cfg.ContextPaths, cfg.LspConfigs)),
		provider.WithMaxTokens(maxTokens),
		provider.WithDebug(cfg.Debug),
	}

	client, err := providers.NewClient(
		model.Provider,
		opts...,
	)
	if err != nil {
		return nil, fmt.Errorf("could not create client: %v", err)
	}

	return client, nil
}
