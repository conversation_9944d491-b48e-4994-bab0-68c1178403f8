package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/message"
	"reflect"
	"strings"
	"testing"
)

// Helper functions
func assertEqual(t *testing.T, expected, actual interface{}) {
	t.Helper()
	if !reflect.DeepEqual(expected, actual) {
		t.<PERSON>rrorf("Expected %v, got %v", expected, actual)
	}
}

func assertContains(t *testing.T, str, substr string) {
	t.Helper()
	if !strings.Contains(str, substr) {
		t.<PERSON>rf("Expected string to contain %q, got %q", substr, str)
	}
}

func assertNoError(t *testing.T, err error) {
	t.Help<PERSON>()
	if err != nil {
		t.<PERSON>("Unexpected error: %v", err)
	}
}

func assertError(t *testing.T, err error) {
	t.Helper()
	if err == nil {
		t.Error("Expected error, got nil")
	}
}

// Test cases for TaskTool Info method
func TestTaskTool_Info(t *testing.T) {
	tool := &taskTool{}
	info := tool.Info()

	assertEqual(t, tools.TaskToolName, info.Name)
	assertContains(t, info.Description, "Launch a new agent")
	assertContains(t, info.Description, "Available agent types")
	assertContains(t, info.Description, "general-purpose")
	assertContains(t, info.Description, "slash commands")

	// Check required parameters
	found := false
	for _, req := range info.Required {
		if req == "prompt" {
			found = true
			break
		}
	}
	if !found {
		t.Error("prompt should be in required parameters")
	}

	found = false
	for _, req := range info.Required {
		if req == "subagent_type" {
			found = true
			break
		}
	}
	if !found {
		t.Error("subagent_type should be in required parameters")
	}

	// Check parameters exist
	if _, exists := info.Parameters["description"]; !exists {
		t.Error("description parameter should exist")
	}
	if _, exists := info.Parameters["prompt"]; !exists {
		t.Error("prompt parameter should exist")
	}
	if _, exists := info.Parameters["subagent_type"]; !exists {
		t.Error("subagent_type parameter should exist")
	}
}

// Test parameter parsing errors
func TestTaskTool_Run_InvalidJSON(t *testing.T) {
	tool := &taskTool{}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("session1").
		WithMessageID("message1").
		Build()

	call := tools.ToolCall{
		Id:    "test-call",
		Name:  tools.TaskToolName,
		Input: `{invalid json}`,
	}

	response, err := tool.Run(toolCtx, call)

	assertNoError(t, err)
	if !response.IsError {
		t.Error("Expected error response")
	}
	assertContains(t, response.Content, "error parsing parameters")
}

func TestTaskTool_Run_MissingPrompt(t *testing.T) {
	tool := &taskTool{}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("session1").
		WithMessageID("message1").
		Build()

	call := tools.ToolCall{
		Id:    "test-call",
		Name:  tools.TaskToolName,
		Input: `{"description": "test", "subagent_type": "general-purpose"}`,
	}

	response, err := tool.Run(toolCtx, call)

	assertNoError(t, err)
	if !response.IsError {
		t.Error("Expected error response")
	}
	assertContains(t, response.Content, "prompt is required")
}

func TestTaskTool_Run_MissingSubagentType(t *testing.T) {
	tool := &taskTool{}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("session1").
		WithMessageID("message1").
		Build()

	call := tools.ToolCall{
		Id:    "test-call",
		Name:  tools.TaskToolName,
		Input: `{"description": "test", "prompt": "test task"}`,
	}

	response, err := tool.Run(toolCtx, call)

	assertNoError(t, err)
	if !response.IsError {
		t.Error("Expected error response")
	}
	assertContains(t, response.Content, "subagent_type is required")
}

func TestTaskTool_Run_MissingContext(t *testing.T) {
	tool := &taskTool{}

	call := tools.ToolCall{
		Id:    "test-call",
		Name:  tools.TaskToolName,
		Input: `{"description": "test", "prompt": "test task", "subagent_type": "general-purpose"}`,
	}

	// Create ToolExecutionContext without session and message IDs to test error handling
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		Build()

	_, err := tool.Run(toolCtx, call)

	assertError(t, err)
	assertContains(t, err.Error(), "session_id and message_id are required")
}

// Test helper methods
func TestTaskTool_GetAvailableAgents(t *testing.T) {
	tool := &taskTool{}

	agents := tool.getAvailableAgents()
	if len(agents) == 0 {
		t.Error("Expected at least one available agent")
	}

	// Should include general-purpose agent
	found := false
	for _, agent := range agents {
		if agent.AgentType == "general-purpose" {
			found = true
			if len(agent.Tools) == 0 {
				t.Error("general-purpose agent should have tools")
			}
			if agent.WhenToUse == "" {
				t.Error("general-purpose agent should have WhenToUse description")
			}
			if agent.Location == "" {
				t.Error("general-purpose agent should have Location")
			}
			if agent.SystemPrompt == "" {
				t.Error("general-purpose agent should have SystemPrompt")
			}
			// Check that SystemPrompt contains expected content
			if !strings.Contains(agent.SystemPrompt, "Claude Code") {
				t.Error("general-purpose agent SystemPrompt should mention Claude Code")
			}
			// Check that it supports all tools (wildcard)
			if len(agent.Tools) == 1 && agent.Tools[0] == "*" {
				// This is expected
			} else {
				t.Error("general-purpose agent should use wildcard '*' for tools")
			}
			break
		}
	}
	if !found {
		t.Error("Should include general-purpose agent")
	}
}

func TestTaskTool_GenerateDescription(t *testing.T) {
	tool := &taskTool{}

	description := tool.generateDescription()

	// Should contain key elements matching JavaScript version
	assertContains(t, description, "Launch a new agent")
	assertContains(t, description, "Available agent types")
	assertContains(t, description, "general-purpose")
	assertContains(t, description, "All available tools") // 应该显示"All available tools"而不是具体工具列表
	assertContains(t, description, "subagent_type parameter")
	assertContains(t, description, "slash commands")
	assertContains(t, description, "Task(description=")
	assertContains(t, description, "Usage notes:")
	assertContains(t, description, "Launch multiple agents concurrently")
	assertContains(t, description, "stateless")

	// Should have 5 usage notes
	noteCount := strings.Count(description, "\n1. ") + strings.Count(description, "\n2. ") +
		strings.Count(description, "\n3. ") + strings.Count(description, "\n4. ") +
		strings.Count(description, "\n5. ")
	if noteCount != 5 {
		t.Errorf("Expected 5 usage notes, found %d", noteCount)
	}
}

func TestTaskTool_GetAgentSystemPrompt(t *testing.T) {
	tool := &taskTool{}

	// Test getting SystemPrompt for known agent type
	systemPrompt := tool.getAgentSystemPrompt("general-purpose")
	if systemPrompt == "" {
		t.Error("Expected non-empty SystemPrompt for general-purpose agent")
	}

	// Should contain key phrases from JavaScript version
	assertContains(t, systemPrompt, "Claude Code")
	assertContains(t, systemPrompt, "Anthropic's official CLI")
	assertContains(t, systemPrompt, "Your strengths:")
	assertContains(t, systemPrompt, "Guidelines:")
	assertContains(t, systemPrompt, "NEVER create files unless")
	assertContains(t, systemPrompt, "avoid using emojis")

	// Test getting SystemPrompt for unknown agent type (should return default)
	unknownPrompt := tool.getAgentSystemPrompt("unknown-agent")
	if unknownPrompt == "" {
		t.Error("Expected fallback SystemPrompt for unknown agent type")
	}

	// Should return the same as general-purpose (the default)
	if unknownPrompt != systemPrompt {
		t.Error("Expected unknown agent type to return general-purpose SystemPrompt as fallback")
	}
}

func TestTaskTool_BuildSynthesisPrompt(t *testing.T) {
	tool := &taskTool{}

	results := []specs.TaskResult{
		{
			Content: []message.ContentPart{
				message.TextContent{Text: "First agent response", Type: "text"},
			},
		},
		{
			Content: []message.ContentPart{
				message.TextContent{Text: "Second agent response", Type: "text"},
			},
		},
	}

	prompt := tool.buildSynthesisPrompt("Find configuration files", results)

	assertContains(t, prompt, "Original task: Find configuration files")
	assertContains(t, prompt, "== AGENT 1 RESPONSE ==")
	assertContains(t, prompt, "First agent response")
	assertContains(t, prompt, "== AGENT 2 RESPONSE ==")
	assertContains(t, prompt, "Second agent response")
	assertContains(t, prompt, "synthesize a comprehensive and cohesive response")
}

func TestTaskTool_GetAgentConfig(t *testing.T) {
	tool := &taskTool{}

	agentConfig := tool.getAgentConfig("general-purpose")
	assertEqual(t, config.AgentTask, agentConfig) // Currently returns default

	agentConfig = tool.getAgentConfig("unknown-type")
	assertEqual(t, config.AgentTask, agentConfig) // Should still return default for now
}

func TestTaskTool_GetAgentTools(t *testing.T) {
	tool := &taskTool{}

	tools := tool.getAgentTools("general-purpose")
	if tools == nil {
		t.Error("Expected tools to be non-nil")
	}

	if len(tools) == 0 {
		t.Error("Expected tools to have length > 0")
	}

	// Should contain extended tools for general-purpose agent (uses "*" wildcard)
	toolNames := make([]string, len(tools))
	for i, tool := range tools {
		toolNames[i] = tool.Info().Name
	}

	// For general-purpose agent with "*" tools, should include basic tools plus web tools
	expectedBasicTools := []string{"Glob", "Grep", "LS", "read"}
	expectedWebTools := []string{"WebFetch", "WebSearch"} // 修正大小写

	// Check basic tools
	for _, expected := range expectedBasicTools {
		found := false
		for _, actual := range toolNames {
			if actual == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected basic tool %s not found in tools list. Available tools: %v", expected, toolNames)
		}
	}

	// Check web tools (should be available for "*" wildcard)
	webToolsFound := 0
	for _, expected := range expectedWebTools {
		for _, actual := range toolNames {
			if actual == expected {
				webToolsFound++
				break
			}
		}
	}

	if webToolsFound < 2 {
		t.Errorf("Expected to find web tools (WebFetch, WebSearch) for general-purpose agent. Found %d web tools. Available tools: %v", webToolsFound, toolNames)
	}

	// Should have more tools than basic TaskAgentTools (which only has 4)
	if len(tools) < 4 {
		t.Errorf("Expected at least 4 tools for general-purpose agent, got %d", len(tools))
	}
}

// Test TaskResult and TaskParams JSON serialization
func TestTaskResult_JSONSerialization(t *testing.T) {
	result := specs.TaskResult{
		Content: []message.ContentPart{
			message.TextContent{Text: "Test content", Type: "text"},
		},
		TotalDurationMs:   1000,
		TotalTokens:       150,
		TotalToolUseCount: 3,
	}

	data, err := json.Marshal(result)
	assertNoError(t, err)

	// For now, just verify we can marshal without error
	// JSON unmarshaling with interface{} types is complex
	if len(data) == 0 {
		t.Error("Marshaled data should not be empty")
	}

	// Test individual fields that should unmarshal correctly
	var partial struct {
		TotalDurationMs   int64 `json:"totalDurationMs"`
		TotalTokens       int   `json:"totalTokens"`
		TotalToolUseCount int   `json:"totalToolUseCount"`
	}

	err = json.Unmarshal(data, &partial)
	assertNoError(t, err)

	assertEqual(t, result.TotalDurationMs, partial.TotalDurationMs)
	assertEqual(t, result.TotalTokens, partial.TotalTokens)
	assertEqual(t, result.TotalToolUseCount, partial.TotalToolUseCount)
}

func TestTaskParams_JSONSerialization(t *testing.T) {
	params := specs.TaskParams{
		Description:  "Test description",
		Prompt:       "Test prompt",
		SubagentType: "general-purpose",
	}

	data, err := json.Marshal(params)
	assertNoError(t, err)

	var unmarshaled specs.TaskParams
	err = json.Unmarshal(data, &unmarshaled)
	assertNoError(t, err)

	assertEqual(t, params.Description, unmarshaled.Description)
	assertEqual(t, params.Prompt, unmarshaled.Prompt)
	assertEqual(t, params.SubagentType, unmarshaled.SubagentType)
}

// Benchmark tests
func BenchmarkTaskTool_BuildSynthesisPrompt(b *testing.B) {
	tool := &taskTool{}

	results := make([]specs.TaskResult, 5)
	for i := range results {
		results[i] = specs.TaskResult{
			Content: []message.ContentPart{
				message.TextContent{Text: fmt.Sprintf("Agent %d response with detailed analysis and code examples", i+1), Type: "text"},
			},
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tool.buildSynthesisPrompt("Complex task requiring analysis", results)
	}
}

func BenchmarkTaskTool_Info(b *testing.B) {
	tool := &taskTool{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tool.Info()
	}
}

// Test edge cases
func TestTaskTool_BuildSynthesisPrompt_EmptyResults(t *testing.T) {
	tool := &taskTool{}

	results := []specs.TaskResult{}
	prompt := tool.buildSynthesisPrompt("Empty task", results)

	assertContains(t, prompt, "Original task: Empty task")
	assertContains(t, prompt, "synthesize a comprehensive and cohesive response")
}

func TestTaskTool_BuildSynthesisPrompt_NonTextContent(t *testing.T) {
	tool := &taskTool{}

	results := []specs.TaskResult{
		{
			Content: []message.ContentPart{
				message.BinaryContent{Path: "test.jpg", MIMEType: "image/jpeg"},
			},
		},
	}

	prompt := tool.buildSynthesisPrompt("Image task", results)

	assertContains(t, prompt, "Original task: Image task")
	assertContains(t, prompt, "== AGENT 1 RESPONSE ==")
	// Should not contain text from binary content
}

// Test constant values
func TestTaskToolName_Constant(t *testing.T) {
	assertEqual(t, "Task", tools.TaskToolName)
}
