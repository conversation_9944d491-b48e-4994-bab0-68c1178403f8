package agent

import (
	"context"

	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/internal/llm/tools"
	coreTools "github.com/qoder-ai/qoder-cli/core/llm/tools"
)

func CoderAgentTools(app app.App, permissions core.PermissionTrigger) []coreTools.BaseTool {
	ctx := context.Background()
	otherTools := GetMcpTools(ctx, permissions, app.GetConfig().McpServers)

	return append(
		[]coreTools.BaseTool{
			//tools.NewWebSearchTool(),         // 依赖ClaudeAPI标准实现，暂时无法使用
			tools.NewBashTool(permissions),     // OK
			tools.NewWebFetchTool(permissions), // OK
			tools.NewEditTool(permissions, app.GetHistoryService()),
			tools.NewMultiEditTool(permissions, app.GetHistoryService()),
			tools.NewGlobTool(),
			tools.NewGrepTool(),
			tools.NewLsTool(),
			tools.NewReadTool(),
			tools.NewWriteTool(permissions, app.GetHistoryService()),
			tools.NewTodoWriteTool(),
			tools.NewExitPlanModeTool(permissions),
			NewTaskTool(app, permissions), // TODO 考虑会话隔离问题
		}, otherTools...,
	)
}

func TaskAgentTools() []coreTools.BaseTool {
	return []coreTools.BaseTool{
		tools.NewGlobTool(),
		tools.NewGrepTool(),
		tools.NewLsTool(),
		tools.NewReadTool(),
	}
}

func ReviewerTools(permissions core.PermissionTrigger) []coreTools.BaseTool {
	return []coreTools.BaseTool{
		tools.NewWebFetchTool(permissions),
		tools.NewReadTool(),
		tools.NewLsTool(),
		tools.NewGlobTool(),
		tools.NewGrepTool(),
		tools.NewBashTool(permissions),
	}
}
