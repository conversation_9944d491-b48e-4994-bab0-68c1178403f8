package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/internal/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/agent"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/llm/provider"
	coreTools "github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"strings"
	"sync"
	"time"
)

type taskTool struct {
	sessions    core.SessionService
	messages    message.MessageService
	files       core.HistoryService
	permissions core.PermissionTrigger
	providers   provider.Service
	config      *config.Config
}

func (t *taskTool) Info() coreTools.ToolInfo {
	return coreTools.ToolInfo{
		Name:        coreTools.TaskToolName,
		Description: t.generateDescription(),
		Parameters: map[string]any{
			"description": map[string]any{
				"type":        "string",
				"description": "A short (3-5 word) description of the task",
			},
			"prompt": map[string]any{
				"type":        "string",
				"description": "The task for the agent to perform",
			},
			"subagent_type": map[string]any{
				"type":        "string",
				"description": "The type of specialized agent to use for this task",
			},
		},
		Required: []string{"description", "prompt", "subagent_type"},
	}
}

// AgentTypeInfo 代理类型信息
type AgentTypeInfo struct {
	AgentType    string
	WhenToUse    string
	Tools        []string
	SystemPrompt string // 添加系统提示词字段
	Location     string // "built-in", "user", "project"
}

// getAvailableAgents 返回可用的代理类型列表
// TODO: 后续可以从配置文件或注册表中动态读取
func (t *taskTool) getAvailableAgents() []AgentTypeInfo {
	return []AgentTypeInfo{
		{
			AgentType: "general-purpose",
			WhenToUse: "General-purpose agent for researching complex questions, searching for code, and executing multi-step tasks. When you are searching for a keyword or file and are not confident that you will find the right match in the first few tries use this agent to perform the search for you.",
			Tools:     []string{"*"}, // 支持所有工具，与JavaScript版本保持一致
			SystemPrompt: `You are an agent for Claude Code, Anthropic's official CLI for Claude. Given the user's message, you should use the tools available to complete the task. Do what has been asked; nothing more, nothing less. When you complete the task simply respond with a detailed writeup.

Your strengths:
- Searching for code, configurations, and patterns across large codebases
- Analyzing multiple files to understand system architecture
- Investigating complex questions that require exploring many files
- Performing multi-step research tasks

Guidelines:
- For file searches: Use Grep or Glob when you need to search broadly. Use Read when you know the specific file path.
- For analysis: Start broad and narrow down. Use multiple search strategies if the first doesn't yield results.
- Be thorough: Check multiple locations, consider different naming conventions, look for related files.
- NEVER create files unless they're absolutely necessary for achieving your goal. ALWAYS prefer editing an existing file to creating a new one.
- NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested.
- In your final response always share relevant file names and code snippets. Any file paths you return in your response MUST be absolute. Do NOT use relative paths.
- For clear communication, avoid using emojis.`,
			Location: "built-in",
		},
		// TODO: 可以从配置中添加更多代理类型
		// 例如：coder, researcher, analyzer等
		// TODO: 支持从用户目录和项目目录动态加载agents
	}
}

func (t *taskTool) generateDescription() string {
	availableAgents := t.getAvailableAgents()

	agentList := ""
	for _, agent := range availableAgents {
		toolsList := ""
		if len(agent.Tools) == 1 && agent.Tools[0] == "*" {
			toolsList = "All available tools"
		} else {
			toolsList = strings.Join(agent.Tools, ", ")
		}
		agentList += fmt.Sprintf("- %s: %s (Tools: %s)\n", agent.AgentType, agent.WhenToUse, toolsList)
	}

	return fmt.Sprintf(`Launch a new agent to handle complex, multi-step tasks autonomously.

Available agent types and the tools they have access to:
%s
When using the Task tool, you must specify a subagent_type parameter to select which agent type to use.

When to use the Task tool:
- When you are instructed to execute custom slash commands. Use the Task tool with the slash command invocation as the entire prompt. The slash command can take arguments. For example: Task(description="Check the file", prompt="/check-file path/to/file.py")
- Complex multi-step tasks requiring autonomous execution
- When you need to search for keywords or files and are not confident about finding the right match quickly
- Tasks that benefit from specialized agent capabilities

When NOT to use the Task tool:
- If you want to read a specific file path, use the read or Glob tool instead of the Task tool, to find the match more quickly
- If you are searching for a specific class definition like "class Foo", use the Glob tool instead, to find the match more quickly
- If you are searching for code within a specific file or set of 2-3 files, use the read tool instead of the Task tool, to find the match more quickly
- Other tasks that are not related to the agent descriptions above

Usage notes:
1. Launch multiple agents concurrently whenever possible, to maximize performance; to do that, use a single message with multiple tool uses
2. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.
3. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.
4. The agent's outputs should generally be trusted
5. Clearly tell the agent whether you expect it to write code or just to do research (search, file reads, web fetches, etc.), since it is not aware of the user's intent`, agentList)
}

func (t *taskTool) Run(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall) (coreTools.ToolResponse, error) {
	startTime := time.Now()

	params, err := specs.TaskSpec.GetParams(call.Input)
	if err != nil {
		return coreTools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	if params.Prompt == "" {
		return coreTools.NewTextErrorResponse("prompt is required"), nil
	}

	if params.SubagentType == "" {
		return coreTools.NewTextErrorResponse("subagent_type is required"), nil
	}

	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return coreTools.ToolResponse{}, fmt.Errorf("session_id and message_id are required")
	}

	// TODO: 获取配置中的parallelTasksCount
	parallelTasksCount := 1 // 默认为1，后续可以从配置读取

	if parallelTasksCount > 1 {
		return t.runParallelAgents(ctx, call, params, parallelTasksCount, startTime)
	} else {
		return t.runSingleAgent(ctx, call, params, startTime)
	}
}

func (t *taskTool) runSingleAgent(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall, params *specs.TaskParams, startTime time.Time) (coreTools.ToolResponse, error) {
	sessionId := ctx.GetSessionId()

	// 获取agent的SystemPrompt
	systemPrompt := t.getAgentSystemPrompt(params.SubagentType)

	// 使用createTaskAgent而不是NewAgent，这样可以使用自定义SystemPrompt
	taskAgent, err := t.createTaskAgent(params.SubagentType, systemPrompt, t.getAgentTools(params.SubagentType))
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating agent: %s", err)
	}

	session, err := t.sessions.CreateTaskSession(ctx, call.Id, sessionId, fmt.Sprintf("Task: %s", params.Description))
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating session: %s", err)
	}

	done, err := taskAgent.Run(ctx, session.Id, params.Prompt)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error running agent: %s", err)
	}

	// 等待agent完成
	var lastEvent agent.Event
	for event := range done {
		if event.Type == agent.AgentEventTypeError {
			return coreTools.ToolResponse{}, fmt.Errorf("agent error: %s", event.Error)
		}
		lastEvent = event
	}

	if lastEvent.Message.Role != message.Assistant {
		return coreTools.NewTextErrorResponse("no response from agent"), nil
	}

	// 更新父会话成本
	if err := t.updateParentSessionCost(ctx, sessionId, session.Id); err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error updating session cost: %s", err)
	}

	// 构建结果
	duration := time.Since(startTime).Milliseconds()
	taskResult := specs.TaskResult{
		Content:           []message.ContentPart{lastEvent.Message.Content()},
		TotalDurationMs:   duration,
		TotalTokens:       100, // 简化统计，实际应该从provider获取
		TotalToolUseCount: 0,   // 简化统计
	}

	resultJson, _ := json.Marshal(taskResult)
	return coreTools.NewTextResponse(string(resultJson)), nil
}

func (t *taskTool) runParallelAgents(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall, params *specs.TaskParams, parallelCount int, startTime time.Time) (coreTools.ToolResponse, error) {
	sessionID := ctx.GetSessionId()

	// 获取agent的SystemPrompt
	systemPrompt := t.getAgentSystemPrompt(params.SubagentType)

	// 创建并行任务
	var wg sync.WaitGroup
	results := make([]specs.TaskResult, parallelCount)
	errors := make([]error, parallelCount)

	enhancedPrompt := fmt.Sprintf("%s\n\nProvide a thorough and complete analysis.", params.Prompt)

	for i := 0; i < parallelCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 使用createTaskAgent而不是NewAgent，这样可以使用自定义SystemPrompt
			taskAgent, err := t.createTaskAgent(params.SubagentType, systemPrompt, t.getAgentTools(params.SubagentType))
			if err != nil {
				errors[index] = err
				return
			}

			session, err := t.sessions.CreateTaskSession(ctx, fmt.Sprintf("%s_%d", call.Id, index), sessionID, fmt.Sprintf("Task Agent %d: %s", index+1, params.Description))
			if err != nil {
				errors[index] = err
				return
			}

			done, err := taskAgent.Run(ctx, session.Id, enhancedPrompt)
			if err != nil {
				errors[index] = err
				return
			}

			// 等待agent完成
			var lastEvent agent.Event
			for event := range done {
				if event.Type == agent.AgentEventTypeError {
					errors[index] = event.Error
					return
				}
				lastEvent = event
			}

			if lastEvent.Message.Role == message.Assistant {
				results[index] = specs.TaskResult{
					Content:           []message.ContentPart{lastEvent.Message.Content()},
					TotalTokens:       100, // 简化统计
					TotalToolUseCount: 0,   // 简化统计
				}
			}
		}(i)
	}

	wg.Wait()

	// 检查错误
	for i, err := range errors {
		if err != nil {
			return coreTools.ToolResponse{}, fmt.Errorf("agent %d error: %s", i+1, err)
		}
	}

	// 合成结果
	synthesisResult, err := t.synthesizeResults(ctx, call, params, results, startTime)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("synthesis error: %s", err)
	}

	return synthesisResult, nil
}

func (t *taskTool) synthesizeResults(ctx coreTools.ToolExecutionContext, call coreTools.ToolCall, params *specs.TaskParams, results []specs.TaskResult, startTime time.Time) (coreTools.ToolResponse, error) {
	sessionID := ctx.GetSessionId()

	// 生成合成提示
	synthesisPrompt := t.buildSynthesisPrompt(params.Prompt, results)

	// 获取agent的SystemPrompt并使用createTaskAgent
	systemPrompt := t.getAgentSystemPrompt(params.SubagentType)
	taskAgent, err := t.createTaskAgent(params.SubagentType, systemPrompt, t.getAgentTools(params.SubagentType))
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating synthesis agent: %s", err)
	}

	session, err := t.sessions.CreateTaskSession(ctx, fmt.Sprintf("%s_synthesis", call.Id), sessionID, fmt.Sprintf("Synthesis: %s", params.Description))
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error creating synthesis session: %s", err)
	}

	done, err := taskAgent.Run(ctx, session.Id, synthesisPrompt)
	if err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error running synthesis agent: %s", err)
	}

	// 等待合成agent完成
	var lastEvent agent.Event
	for event := range done {
		if event.Type == agent.AgentEventTypeError {
			return coreTools.ToolResponse{}, fmt.Errorf("synthesis agent error: %s", event.Error)
		}
		lastEvent = event
	}

	if lastEvent.Message.Role != message.Assistant {
		return coreTools.NewTextErrorResponse("no response from synthesis agent"), nil
	}

	// 计算总计数据
	totalTokens := 100 // 简化统计
	totalToolUseCount := 0

	for _, r := range results {
		totalTokens += r.TotalTokens
		totalToolUseCount += r.TotalToolUseCount
	}

	// 更新父会话成本
	if err := t.updateParentSessionCost(ctx, sessionID, session.Id); err != nil {
		return coreTools.ToolResponse{}, fmt.Errorf("error updating session cost: %s", err)
	}

	duration := time.Since(startTime).Milliseconds()
	finalResult := specs.TaskResult{
		Content:           []message.ContentPart{lastEvent.Message.Content()},
		TotalDurationMs:   duration,
		TotalTokens:       totalTokens,
		TotalToolUseCount: totalToolUseCount,
	}

	resultJson, _ := json.Marshal(finalResult)
	return coreTools.NewTextResponse(string(resultJson)), nil
}

func (t *taskTool) buildSynthesisPrompt(originalTask string, results []specs.TaskResult) string {
	prompt := fmt.Sprintf("Original task: %s\n\nI've assigned multiple agents to tackle this task. Each agent has analyzed the problem and provided their findings.\n\n", originalTask)

	for i, result := range results {
		content := ""
		for _, part := range result.Content {
			if textPart, ok := part.(message.TextContent); ok {
				content += textPart.String() + "\n\n"
			}
		}
		prompt += fmt.Sprintf("== AGENT %d RESPONSE ==\n%s\n\n", i+1, content)
	}

	prompt += `Based on all the information provided by these agents, synthesize a comprehensive and cohesive response that:
1. Combines the key insights from all agents
2. Resolves any contradictions between agent findings
3. Presents a unified solution that addresses the original task
4. Includes all important details and code examples from the individual responses
5. Is well-structured and complete

Your synthesis should be thorough but focused on the original task.`

	return prompt
}

func (t *taskTool) getAgentConfig(subagentType string) config.AgentName {
	// TODO: 实现从配置或注册表获取代理配置
	// 目前返回默认的TaskAgent配置
	return config.AgentTask
}

func (t *taskTool) getAgentTools(subagentType string) []coreTools.BaseTool {
	// 获取指定类型agent的工具配置
	availableAgents := t.getAvailableAgents()

	var agentTools []string
	for _, agent := range availableAgents {
		if agent.AgentType == subagentType {
			agentTools = agent.Tools
			break
		}
	}

	// 如果指定了"*"，返回扩展的工具集（类似CoderAgentTools但不包括递归的TaskTool）
	if len(agentTools) == 1 && agentTools[0] == "*" {
		return []coreTools.BaseTool{
			tools.NewGlobTool(),
			tools.NewGrepTool(),
			tools.NewLsTool(),
			tools.NewReadTool(),
			tools.NewWebFetchTool(t.permissions),
			//tools.NewWebSearchTool(),
			tools.NewBashTool(t.permissions),
			tools.NewEditTool(t.permissions, t.files),
			tools.NewMultiEditTool(t.permissions, t.files),
			tools.NewWriteTool(t.permissions, t.files),
			tools.NewTodoWriteTool(),
			tools.NewExitPlanModeTool(t.permissions),
		}
	}

	// TODO: 根据具体的工具名称列表返回对应工具
	// 目前仍然返回基础的TaskAgentTools作为默认
	return TaskAgentTools()
}

func (t *taskTool) updateParentSessionCost(ctx context.Context, parentSessionID, childSessionID string) error {
	childSession, err := t.sessions.Get(ctx, childSessionID)
	if err != nil {
		return err
	}

	parentSession, err := t.sessions.Get(ctx, parentSessionID)
	if err != nil {
		return err
	}

	parentSession.Cost += childSession.Cost
	return t.sessions.Save(ctx, parentSession)
}

func NewTaskTool(app app.App, permissions core.PermissionTrigger) coreTools.BaseTool {
	return &taskTool{
		permissions: permissions,
		files:       app.GetHistoryService(),
		sessions:    app.GetSessionService(),
		messages:    app.GetMessageService(),
		providers:   app.GetProviderService(),
		config:      app.GetConfig(),
	}
}

// 保留原有的AgentTool作为向后兼容
//func NewAgentTool(sessions session.Service, messages message.Service) tools.BaseTool {
//	return NewTaskTool(sessions, messages)
//}

// createTaskAgent 创建一个使用自定义SystemPrompt的agent
// 与NewAgent不同，这个函数允许指定自定义的SystemPrompt
func (t *taskTool) createTaskAgent(
	subagentType string,
	customSystemPrompt string,
	agentTools []coreTools.BaseTool,
) (agent.Agent, error) {
	// 获取基础的agent配置（使用task agent的配置作为基础）
	agentConfig, ok := t.config.GetAgent(config.AgentTask)
	if !ok {
		return nil, fmt.Errorf("task agent configuration not found")
	}

	model, ok := models.SupportedModels[agentConfig.Model]
	if !ok {
		return nil, fmt.Errorf("model %s not supported", agentConfig.Model)
	}

	providerCfg, ok := t.config.GetProvider(model.Provider)
	if !ok {
		return nil, fmt.Errorf("client %s not supported", model.Provider)
	}
	if providerCfg.Disabled {
		return nil, fmt.Errorf("client %s is not enabled", model.Provider)
	}

	maxTokens := model.DefaultMaxTokens
	if agentConfig.MaxTokens > 0 {
		maxTokens = agentConfig.MaxTokens
	}

	// 创建provider，使用自定义的SystemPrompt
	opts := []provider.ClientOption{
		provider.WithAPIKey(providerCfg.ApiKey),
		provider.WithModel(model),
		provider.WithSystemMessage(customSystemPrompt), // 使用自定义的SystemPrompt
		provider.WithMaxTokens(maxTokens),
		provider.WithDebug(t.config.Debug),
	}

	client, err := t.providers.NewClient(model.Provider, opts...)
	if err != nil {
		return nil, fmt.Errorf("could not create client: %v", err)
	}

	// 创建agent实例
	taskAgent := &agentRunner{
		Broker:         pubsub.NewBroker[agent.Event](),
		client:         client,
		messages:       t.messages,
		sessions:       t.sessions,
		tools:          agentTools,
		activeRequests: sync.Map{},
		// 注意：Task agent不需要title和summarize client
	}

	return taskAgent, nil
}

// getAgentSystemPrompt 获取指定agent类型的SystemPrompt
func (t *taskTool) getAgentSystemPrompt(subagentType string) string {
	availableAgents := t.getAvailableAgents()

	for _, agent := range availableAgents {
		if agent.AgentType == subagentType {
			return agent.SystemPrompt
		}
	}

	// 如果找不到指定类型，返回默认的general-purpose prompt
	if len(availableAgents) > 0 {
		return availableAgents[0].SystemPrompt
	}

	// 最后的fallback
	return "You are a helpful assistant."
}
