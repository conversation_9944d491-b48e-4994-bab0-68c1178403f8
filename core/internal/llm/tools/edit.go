package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/utils/diff"
)

func Validate(params *specs.EditParams) tools.ToolResponse {
	// 1. 修改内容相同
	if params.OldString == params.NewString {
		return tools.NewTextErrorResponse("No changes to make: old_string and new_string are exactly the same.")
	}

	// 2. TODO 文件在忽略清单当中
	// File is in a directory that is ignored by your project configuration.
	//path := params.FilePath
	//if filepath.IsAbs(path) {
	//	path = params.FilePath[len(config.WorkingDirectory()):]
	//}

	// 3. 文件已经存在，且需要新建文件
	fileInfo, err := os.Stat(params.FilePath)
	isFileExists := fileInfo != nil && err == nil
	if params.OldString == "" {
		if isFileExists {
			return tools.NewTextErrorResponse("Cannot create new file - file already exists.")
		}
		return tools.ToolResponse{}
	}

	// 4. 文件不存在
	if !isFileExists {
		// + Current working directory: ${K} + Did you mean ${V}?
		return tools.NewTextErrorResponse("File does not exist.")
	}

	// 5. Notebook 文件使用对应的工具
	if strings.HasSuffix(params.FilePath, ".ipynb") {
		return tools.NewTextErrorResponse("File is a Jupyter Notebook. Use the NotebookEdit to edit this file.")
	}

	// 6. 检查文件是否被Read工具读取过
	if getLastReadTime(params.FilePath).IsZero() {
		return tools.NewTextErrorResponse("File has not been read yet. Read it first before writing to it.")
	}

	// 7. 文件被修改后需要重新读取
	if fileInfo.ModTime().After(getLastReadTime(params.FilePath)) {
		return tools.NewTextErrorResponse("File has been modified since read, either by the user or by a linter. Read it again before attempting to write it.")
	}

	// Read file content to check old_string uniqueness
	content, err := os.ReadFile(params.FilePath)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("Error reading file: %s", err))
	}
	oldContent := string(content)
	oldContent = strings.ReplaceAll(oldContent, "\r\n", "\n")

	// 8. Check if old_string exists in the file
	count := strings.Count(oldContent, params.OldString)
	if count <= 0 {
		return tools.NewTextErrorResponse("String to replace not found in file.\nString: " + params.OldString)
	}

	// 9. 超过一个被替换的内容
	if count > 1 && !params.ReplaceAll {
		return tools.NewTextErrorResponse(fmt.Sprintf("Found %d matches of the string to replace, but replace_all is false. To replace all occurrences, set replace_all to true. To replace only one occurrence, please provide more context to uniquely identify the instance.\nString: %s", count, params.OldString))
	}
	return tools.ToolResponse{}
}

type editTool struct {
	//lspClients  map[string]*lsp.Client
	permissions core.PermissionTrigger
	files       core.HistoryService
}

const (
	editDescription = `Performs exact string replacements in files.

Usage:
- You must use your ` + "`Read`" + ` tool at least once in the conversation before editing. This tool will error if you attempt an edit without reading the file.
- When editing text from Read tool output, ensure you preserve the exact indentation (tabs/spaces) as it appears AFTER the line number prefix. The line number prefix format is: spaces + line number + tab. Everything after that tab is the actual file content to match. Never include any part of the line number prefix in the old_string or new_string.
- ALWAYS prefer editing existing files in the codebase. NEVER write new files unless explicitly required.
- Only use emojis if the user explicitly requests it. Avoid adding emojis to files unless asked.
- The edit will FAIL if ` + "`old_string`" + ` is not unique in the file. Either provide a larger string with more surrounding context to make it unique or use ` + "`replace_all`" + ` to change every instance of ` + "`old_string`" + `.
- Use ` + "`replace_all`" + ` for replacing and renaming strings across the file. This parameter is useful if you want to rename a variable for instance.`
)

func NewEditTool(permissions core.PermissionTrigger, files core.HistoryService) tools.BaseTool {
	return &editTool{
		//lspClients:  lspClients,
		permissions: permissions,
		files:       files,
	}
}

func (e *editTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.EditToolName,
		Description: editDescription,
		Parameters: map[string]any{
			"file_path": map[string]any{
				"type":        "string",
				"description": "The absolute path to the file to modify",
			},
			"old_string": map[string]any{
				"type":        "string",
				"description": "The text to replace",
			},
			"new_string": map[string]any{
				"type":        "string",
				"description": "The text to replace it with",
			},
			"replace_all": map[string]any{ // TODO 新增，可选，default=false
				"type":        "boolean",
				"description": "Replace all occurrences of old_string (default false)",
			},
		},
		Required: []string{"file_path", "old_string", "new_string"},
	}
}

func (e *editTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.EditSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	if params.FilePath == "" {
		return tools.NewTextErrorResponse("file_path is required"), nil
	}

	if !filepath.IsAbs(params.FilePath) {
		// Get working directory from tool execution context
		wd := ctx.GetWorkingDir()
		params.FilePath = filepath.Join(wd, params.FilePath)
	}

	if response := Validate(params); response.IsError {
		return response, nil
	}

	oldContent := ""
	newContent := params.NewString
	if len(params.OldString) != 0 {
		content, err := os.ReadFile(params.FilePath)
		if err != nil {
			return tools.ToolResponse{}, fmt.Errorf("failed to read file: %w", err)
		}

		oldContent = string(content)
		if params.ReplaceAll {
			newContent = strings.ReplaceAll(oldContent, params.OldString, params.NewString)
		} else {
			newContent = strings.Replace(oldContent, params.OldString, params.NewString, 1)
		}
	}

	applier := FileApplier{
		ToolName:    tools.EditToolName,
		permissions: e.permissions,
		files:       e.files,
		FilePath:    params.FilePath,
		OldContent:  oldContent,
		NewContent:  newContent,
	}
	return applier.Apply(ctx)
}

type FileApplier struct {
	ToolName    string
	permissions core.PermissionTrigger
	files       core.HistoryService

	FilePath   string
	OldContent string
	NewContent string
}

func (a *FileApplier) Apply(ctx tools.ToolExecutionContext) (tools.ToolResponse, error) {
	sessionID := ctx.GetSessionId()
	messageID := ctx.GetMessageId()
	if sessionID == "" || messageID == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for editing a file")
	}

	diff, additions, removals := diff.GenerateDiff(a.OldContent, a.NewContent, a.FilePath, ctx.GetWorkingDir())
	if !a.requestPermission(ctx, sessionID, a.FilePath, diff) {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	if len(a.OldContent) == 0 {
		if err := a.createNewFile(ctx, sessionID, a.FilePath, a.NewContent); err != nil {
			return tools.ToolResponse{}, err
		}
	} else {
		if err := a.replaceContent(ctx, sessionID, a.FilePath, a.OldContent, a.NewContent); err != nil {
			return tools.ToolResponse{}, err
		}
	}

	// TODO 返回修改内容的概况
	return tools.WithResponseMetadata(
		tools.NewTextResponse("Content written in file: "+a.FilePath),
		specs.EditResponseMetadata{
			Diff:      diff,
			Additions: additions,
			Removals:  removals,
		}), nil
}

func (a *FileApplier) requestPermission(ctx tools.ToolExecutionContext, sessionID, filePath, diff string) bool {
	// Get root directory from tool execution context
	rootDir := ctx.GetWorkingDir()
	permissionPath := filepath.Dir(filePath)
	if strings.HasPrefix(filePath, rootDir) {
		permissionPath = rootDir
	}
	return a.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionID,
			Path:        permissionPath,
			ToolName:    a.ToolName,
			Action:      "write",
			Description: fmt.Sprintf("%s content in file %s", a.ToolName, filePath),
			Params: specs.EditPermissionsParams{
				FilePath: filePath,
				Diff:     diff,
			},
		},
	)
}

func (a *FileApplier) createNewFile(ctx context.Context, sessionID, filePath, content string) error {
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return fmt.Errorf("failed to create parent directories: %w", err)
	}

	err := os.WriteFile(filePath, []byte(content), 0o644)
	if err != nil {
		return fmt.Errorf("failed to write file: %w", err)
	}

	// File can't be in the history so we create a new file history
	_, err = a.files.Create(ctx, sessionID, filePath, "")
	if err != nil {
		// Log error but don't fail the operation
		return fmt.Errorf("error creating file history: %w", err)
	}

	// Add the new content to the file history
	_, err = a.files.CreateVersion(ctx, sessionID, filePath, content)
	if err != nil {
		// Log error but don't fail the operation
		logging.Debug("Error creating file history version", "error", err)
	}

	recordFileWrite(filePath)
	recordFileRead(filePath)
	return nil
}

func (a *FileApplier) replaceContent(ctx context.Context, sessionID, filePath, oldContent, newContent string) error {

	err := os.WriteFile(filePath, []byte(newContent), 0o644)
	if err != nil {
		return fmt.Errorf("failed to write file: %w", err)
	}

	// Check if file exists in history
	file, err := a.files.GetByPathAndSession(ctx, filePath, sessionID)
	if err != nil {
		_, err = a.files.Create(ctx, sessionID, filePath, oldContent)
		if err != nil {
			// Log error but don't fail the operation
			return fmt.Errorf("error creating file history: %w", err)
		}
	}
	if file.Content != oldContent {
		// User Manually changed the content store an intermediate version
		_, err = a.files.CreateVersion(ctx, sessionID, filePath, oldContent)
		if err != nil {
			logging.Debug("Error creating file history version", "error", err)
		}
	}
	// Store the new version
	_, err = a.files.CreateVersion(ctx, sessionID, filePath, newContent)
	if err != nil {
		logging.Debug("Error creating file history version", "error", err)
	}

	recordFileWrite(filePath)
	recordFileRead(filePath)
	return nil
}
