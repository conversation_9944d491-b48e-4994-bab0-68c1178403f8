package tools

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"os"
	"strings"
	"testing"
)

func TestTodoWriteTool_Info(t *testing.T) {
	tool := NewTodoWriteTool()
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.TodoWriteToolName {
		t.<PERSON>rf("Expected name %s, got %s", tools.TodoWriteToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["todos"]; !ok {
		t.Error("todos parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) != 1 || info.Required[0] != "todos" {
		t.Error("todos should be the only required parameter")
	}
}

func TestTodoWriteTool_Run_Success(t *testing.T) {
	tool := NewTodoWriteTool()

	// 创建带有sessionID的上下文
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		Build()

	params := specs.TodoWriteParams{
		Todos: []specs.Todo{
			{
				ID:       "todo-1",
				Content:  "实现用户认证功能",
				Status:   specs.TodoStatusInProgress,
				Priority: specs.TodoPriorityHigh,
			},
			{
				ID:       "todo-2",
				Content:  "编写单元测试",
				Status:   specs.TodoStatusPending,
				Priority: specs.TodoPriorityMedium,
			},
		},
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.TodoWriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	expectedContent := "Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"
	if response.Content != expectedContent {
		t.Errorf("Expected content %q, got %q", expectedContent, response.Content)
	}

	// 验证文件是否被正确保存
	todoTool := tool.(*todoWriteTool)
	savedTodos, err := todoTool.loadTodos("test-session-123")
	if err != nil {
		t.Fatalf("Failed to load saved todos: %v", err)
	}

	if len(savedTodos) != 2 {
		t.Errorf("Expected 2 todos, got %d", len(savedTodos))
	}

	if savedTodos[0].ID != "todo-1" || savedTodos[0].Content != "实现用户认证功能" {
		t.Error("First todo was not saved correctly")
	}

	// 清理测试文件
	filePath, _ := todoTool.getTodoFilePath("test-session-123")
	os.Remove(filePath)
}

func TestTodoWriteTool_Run_EmptyTodos(t *testing.T) {
	tool := NewTodoWriteTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-456").
		Build()

	params := specs.TodoWriteParams{
		Todos: []specs.Todo{}, // 空todos列表
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.TodoWriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 空todos列表应该是有效的
	if response.IsError {
		t.Errorf("Expected successful response for empty todos, got error: %s", response.Content)
	}

	// 清理测试文件
	todoTool := tool.(*todoWriteTool)
	filePath, _ := todoTool.getTodoFilePath("test-session-456")
	os.Remove(filePath)
}

func TestTodoWriteTool_Run_InvalidTodo(t *testing.T) {
	tool := NewTodoWriteTool()
	ctx := context.WithValue(context.Background(), tools.SessionIDContextKey, "test-session-789")
	toolCtx := tools.NewToolExecutionContextBuilder(ctx).
		WithSessionID("test-session-456").
		Build()
	params := specs.TodoWriteParams{
		Todos: []specs.Todo{
			{
				ID:       "", // 无效：空ID
				Content:  "测试任务",
				Status:   specs.TodoStatusPending,
				Priority: specs.TodoPriorityMedium,
			},
		},
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.TodoWriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for invalid todo")
	}

	if !contains(response.Content, "id cannot be empty") {
		t.Errorf("Expected error about empty Id, got: %s", response.Content)
	}
}

func TestTodoWriteTool_Run_InvalidStatus(t *testing.T) {
	tool := NewTodoWriteTool()
	ctx := context.WithValue(context.Background(), tools.SessionIDContextKey, "test-session-999")
	toolCtx := tools.NewToolExecutionContextBuilder(ctx).
		WithSessionID("test-session-456").
		Build()
	params := specs.TodoWriteParams{
		Todos: []specs.Todo{
			{
				ID:       "todo-1",
				Content:  "测试任务",
				Status:   specs.TodoStatus("invalid-status"), // 无效状态
				Priority: specs.TodoPriorityMedium,
			},
		},
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.TodoWriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for invalid status")
	}

	if !contains(response.Content, "invalid status") {
		t.Errorf("Expected error about invalid status, got: %s", response.Content)
	}
}

func TestTodoWriteTool_Run_MissingSessionID(t *testing.T) {
	tool := NewTodoWriteTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		Build()
	params := specs.TodoWriteParams{
		Todos: []specs.Todo{
			{
				ID:       "todo-1",
				Content:  "测试任务",
				Status:   specs.TodoStatusPending,
				Priority: specs.TodoPriorityMedium,
			},
		},
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.TodoWriteToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for missing session Id")
	}

	if response.Content != "session Id is required" {
		t.Errorf("Expected session Id error, got: %s", response.Content)
	}
}

func TestTodoWriteTool_Run_InvalidJSON(t *testing.T) {
	tool := NewTodoWriteTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-json").
		Build()
	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.TodoWriteToolName,
		Input: "invalid json",
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for invalid JSON")
	}

	if response.Content != "invalid parameters" {
		t.Errorf("Expected invalid parameters error, got: %s", response.Content)
	}
}

func TestTodoWriteTool_LoadAndSave(t *testing.T) {
	tool := &todoWriteTool{}
	sessionID := "test-load-save"

	// 测试加载不存在的文件
	todos, err := tool.loadTodos(sessionID)
	if err != nil {
		t.Fatalf("Failed to load non-existent todos: %v", err)
	}

	if len(todos) != 0 {
		t.Errorf("Expected empty todos for non-existent file, got %d todos", len(todos))
	}

	// 保存todos
	testTodos := []specs.Todo{
		{
			ID:       "test-1",
			Content:  "测试任务1",
			Status:   specs.TodoStatusPending,
			Priority: specs.TodoPriorityHigh,
		},
		{
			ID:       "test-2",
			Content:  "测试任务2",
			Status:   specs.TodoStatusCompleted,
			Priority: specs.TodoPriorityLow,
		},
	}

	err = tool.saveTodos(sessionID, testTodos)
	if err != nil {
		t.Fatalf("Failed to save todos: %v", err)
	}

	// 重新加载
	loadedTodos, err := tool.loadTodos(sessionID)
	if err != nil {
		t.Fatalf("Failed to load saved todos: %v", err)
	}

	if len(loadedTodos) != 2 {
		t.Errorf("Expected 2 todos, got %d", len(loadedTodos))
	}

	// 验证内容
	if loadedTodos[0].ID != "test-1" || loadedTodos[0].Content != "测试任务1" {
		t.Error("First todo was not loaded correctly")
	}

	if loadedTodos[1].Status != specs.TodoStatusCompleted || loadedTodos[1].Priority != specs.TodoPriorityLow {
		t.Error("Second todo was not loaded correctly")
	}

	// 清理测试文件
	filePath, _ := tool.getTodoFilePath(sessionID)
	os.Remove(filePath)
}

func TestTodoWriteTool_ValidateTodos(t *testing.T) {
	tool := &todoWriteTool{}

	// 测试有效的todos
	validTodos := []specs.Todo{
		{
			ID:       "valid-1",
			Content:  "有效任务",
			Status:   specs.TodoStatusPending,
			Priority: specs.TodoPriorityMedium,
		},
	}

	err := tool.validateTodos(validTodos)
	if err != nil {
		t.Errorf("Expected valid todos to pass validation, got error: %v", err)
	}

	// 测试空内容
	invalidTodos := []specs.Todo{
		{
			ID:       "invalid-1",
			Content:  "", // 空内容
			Status:   specs.TodoStatusPending,
			Priority: specs.TodoPriorityMedium,
		},
	}

	err = tool.validateTodos(invalidTodos)
	if err == nil {
		t.Error("Expected validation error for empty content")
	}

	// 测试空ID
	invalidTodos = []specs.Todo{
		{
			ID:       "", // 空ID
			Content:  "有效内容",
			Status:   specs.TodoStatusPending,
			Priority: specs.TodoPriorityMedium,
		},
	}

	err = tool.validateTodos(invalidTodos)
	if err == nil {
		t.Error("Expected validation error for empty Id")
	}
}

func TestTodoWriteTool_ValidateEnums(t *testing.T) {
	// 测试状态验证
	validStatuses := []specs.TodoStatus{specs.TodoStatusPending, specs.TodoStatusInProgress, specs.TodoStatusCompleted}
	for _, status := range validStatuses {
		if !isValidStatus(status) {
			t.Errorf("Status %s should be valid", status)
		}
	}

	if isValidStatus(specs.TodoStatus("invalid")) {
		t.Error("Invalid status should not be valid")
	}

	// 测试优先级验证
	validPriorities := []specs.TodoPriority{specs.TodoPriorityHigh, specs.TodoPriorityMedium, specs.TodoPriorityLow}
	for _, priority := range validPriorities {
		if !isValidPriority(priority) {
			t.Errorf("Priority %s should be valid", priority)
		}
	}

	if isValidPriority(specs.TodoPriority("invalid")) {
		t.Error("Invalid priority should not be valid")
	}
}

func TestTodoWriteTool_UpdateExistingTodos(t *testing.T) {
	tool := NewTodoWriteTool()
	sessionID := "test-update"
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-update").
		Build()
	// 先保存一些todos
	initialTodos := specs.TodoWriteParams{
		Todos: []specs.Todo{
			{
				ID:       "todo-1",
				Content:  "初始任务1",
				Status:   specs.TodoStatusPending,
				Priority: specs.TodoPriorityHigh,
			},
			{
				ID:       "todo-2",
				Content:  "初始任务2",
				Status:   specs.TodoStatusPending,
				Priority: specs.TodoPriorityMedium,
			},
		},
	}

	input, _ := json.Marshal(initialTodos)
	call := tools.ToolCall{Id: "test-1", Name: tools.TodoWriteToolName, Input: string(input)}

	_, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to save initial todos: %v", err)
	}

	// 更新todos
	updatedTodos := specs.TodoWriteParams{
		Todos: []specs.Todo{
			{
				ID:       "todo-1",
				Content:  "更新的任务1",
				Status:   specs.TodoStatusCompleted, // 状态改为完成
				Priority: specs.TodoPriorityHigh,
			},
			{
				ID:       "todo-3", // 新任务
				Content:  "新增任务3",
				Status:   specs.TodoStatusInProgress,
				Priority: specs.TodoPriorityLow,
			},
		},
	}

	input, _ = json.Marshal(updatedTodos)
	call = tools.ToolCall{Id: "test-2", Name: tools.TodoWriteToolName, Input: string(input)}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to update todos: %v", err)
	}

	if response.IsError {
		t.Errorf("Expected successful update, got error: %s", response.Content)
	}

	// 验证更新结果
	todoTool := tool.(*todoWriteTool)
	savedTodos, err := todoTool.loadTodos(sessionID)
	if err != nil {
		t.Fatalf("Failed to load updated todos: %v", err)
	}

	if len(savedTodos) != 2 {
		t.Errorf("Expected 2 todos after update, got %d", len(savedTodos))
	}

	// 验证第一个任务被更新
	found := false
	for _, todo := range savedTodos {
		if todo.ID == "todo-1" {
			if todo.Content != "更新的任务1" || todo.Status != specs.TodoStatusCompleted {
				t.Error("Todo-1 was not updated correctly")
			}
			found = true
			break
		}
	}
	if !found {
		t.Error("Todo-1 not found in updated list")
	}

	// 清理测试文件
	filePath, _ := todoTool.getTodoFilePath(sessionID)
	os.Remove(filePath)
}

// 辅助函数：检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}
