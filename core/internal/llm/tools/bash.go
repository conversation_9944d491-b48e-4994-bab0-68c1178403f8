package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/internal/llm/tools/shell"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/utils"
)

type bashTool struct {
	permissions  core.PermissionTrigger
	snapshotFile string
	snapshotMu   sync.Mutex
}

// CommandExitCodeInterpreter 命令退出码解释器
type CommandExitCodeInterpreter func(exitCode int, stdout, stderr string) ExitCodeResult

type ExitCodeResult struct {
	IsError bool   `json:"is_error"`
	Message string `json:"message,omitempty"`
}

const (
	DefaultTimeout  = 2 * 60 * 1000  // 2 minutes in milliseconds
	MaxTimeout      = 10 * 60 * 1000 // 10 minutes in milliseconds
	MaxOutputLength = 30000
	bashDescription = `Executes a given bash command in a persistent shell session with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Directory Verification:
   - If the command will create new directories or files, first use the LS tool to verify the parent directory exists and is the correct location
   - For example, before running "mkdir foo/bar", first use LS to check that "foo" exists and is the intended parent directory

2. Command Execution:
   - Always quote file paths that contain spaces with double quotes (e.g., cd "path with spaces/file.txt")
   - Examples of proper quoting:
     - cd "/Users/<USER>/My Documents" (correct)
     - cd /Users/<USER>/My Documents (incorrect - will fail)
     - python "/path/with spaces/script.py" (correct)
     - python /path/with spaces/script.py (incorrect - will fail)
   - After ensuring proper quoting, execute the command.
   - Capture the output of the command.

Usage notes:
  - The command argument is required.
  - You can specify an optional timeout in milliseconds (up to ` + string(rune(MaxTimeout)) + `ms / ` + string(rune(MaxTimeout/60000)) + ` minutes). If not specified, commands will timeout after ` + string(rune(DefaultTimeout)) + `ms (` + string(rune(DefaultTimeout/60000)) + ` minutes).
  - It is very helpful if you write a clear, concise description of what this command does in 5-10 words.
  - If the output exceeds ` + string(rune(MaxOutputLength)) + ` characters, output will be truncated before being returned to you.
  - VERY IMPORTANT: You MUST avoid using search commands like ` + "`find`" + ` and ` + "`grep`" + `. Instead use Grep, Glob, or Task to search. You MUST avoid read tools like ` + "`cat`" + `, ` + "`head`" + `, ` + "`tail`" + `, and ` + "`ls`" + `, and use Read and LS to read files.
  - If you _still_ need to run ` + "`grep`" + `, STOP. ALWAYS USE ripgrep at ` + "`rg`" + ` first, which all Claude Code users have pre-installed.
  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines (newlines are ok in quoted strings).
  - Try to maintain your current working directory throughout the session by using absolute paths and avoiding usage of ` + "`cd`" + `. You may use ` + "`cd`" + ` if the User explicitly requests it.
    <good-example>
    pytest /foo/bar/tests
    </good-example>
    <bad-example>
    cd /foo/bar && pytest tests
    </bad-example>

# Committing changes with git

When the user asks you to create a new git commit, follow these steps carefully:

1. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following bash commands in parallel, each using the Bash tool:
  - Run a git status command to see all untracked files.
  - Run a git diff command to see both staged and unstaged changes that will be committed.
  - Run a git log command to see recent commit messages, so that you can follow this repository's commit message style.
2. Analyze all staged changes (both previously staged and newly added) and draft a commit message:
  - Summarize the nature of the changes (eg. new feature, enhancement to an existing feature, bug fix, refactoring, test, docs, etc.). Ensure the message accurately reflects the changes and their purpose (i.e. "add" means a wholly new feature, "update" means an enhancement to an existing feature, "fix" means a bug fix, etc.).
  - Check for any sensitive information that shouldn't be committed
  - Draft a concise (1-2 sentences) commit message that focuses on the "why" rather than the "what"
  - Ensure it accurately reflects the changes and their purpose
3. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following commands in parallel:
   - Add relevant untracked files to the staging area.
   - Create the commit with a message ending with: 🤖 Generated with [Claude Code][https://claude.ai/code]
   - Run git status to make sure the commit succeeded.
4. If the commit fails due to pre-commit hook changes, retry the commit ONCE to include these automated changes. If it fails again, it usually means a pre-commit hook is preventing the commit. If the commit succeeds but you notice that files were modified by the pre-commit hook, you MUST amend your commit to include them.

Important notes:
- NEVER update the git config
- NEVER run additional commands to read or explore code, besides git bash commands
- NEVER use the TodoWrite or Task tools
- DO NOT push to the remote repository unless the user explicitly asks you to do so
- IMPORTANT: Never use git commands with the -i flag (like git rebase -i or git add -i) since they require interactive input which is not supported.
- If there are no changes to commit (i.e., no untracked files and no modifications), do not create an empty commit
- In order to ensure good formatting, ALWAYS pass the commit message via a HEREDOC, a la this example:
<example>
git commit -m "$(cat <<'EOF'
   Commit message here.

   🤖 Generated with [Claude Code][https://claude.ai/code]
EOF
)"
</example>

# Creating pull requests
Use the gh command via the Bash tool for ALL GitHub-related tasks including working with issues, pull requests, checks, and releases. If given a Github URL use the gh command to get the information needed.

IMPORTANT: When the user asks you to create a pull request, follow these steps carefully:

1. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following bash commands in parallel using the ${eU} tool, in order to understand the current state of the branch since it diverged from the main branch:
   - Run a git status command to see all untracked files
   - Run a git diff command to see both staged and unstaged changes that will be committed
   - Check if the current branch tracks a remote branch and is up to date with the remote, so you know if you need to push to the remote
   - Run a git log command and ` + "`git diff [base-branch]...HEAD`" + ` to understand the full commit history for the current branch (from the time it diverged from the base branch)
2. Analyze all changes that will be included in the pull request, making sure to look at all relevant commits (NOT just the latest commit, but ALL commits that will be included in the pull request!!!), and draft a pull request summary
3. You have the capability to call multiple tools in a single response. When multiple independent pieces of information are requested, batch your tool calls together for optimal performance. ALWAYS run the following commands in parallel:
   - Create new branch if needed
   - Push to remote with -u flag if needed
   - Create PR using gh pr create with the format below. Use a HEREDOC to pass the body to ensure correct formatting.
<example>
gh pr create --title "the pr title" --body "$(cat <<'EOF'
## Summary
<1-3 bullet points>

## Test plan
[Checklist of TODOs for testing the pull request...]

	🤖 Generated with [Claude Code][https://claude.ai/code]
EOF
)"
</example>

Important:
- NEVER update the git config
- DO NOT use the TodoWrite or Task tools
- Return the PR URL when you're done, so the user can see it

# Other common operations
- View comments on a Github PR: gh api repos/foo/bar/pulls/123/comments`
)

// 智能退出码解释器映射 - 基于JavaScript版本的vQ4
var exitCodeInterpreters = map[string]CommandExitCodeInterpreter{
	"grep": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("grep failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "No matches found"}
		}
		return ExitCodeResult{IsError: false}
	},
	"rg": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("ripgrep failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "No matches found"}
		}
		return ExitCodeResult{IsError: false}
	},
	"find": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("find failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Some directories were inaccessible"}
		}
		return ExitCodeResult{IsError: false}
	},
	"diff": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("diff failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Files differ"}
		}
		return ExitCodeResult{IsError: false}
	},
	"test": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("test failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Condition is false"}
		}
		return ExitCodeResult{IsError: false}
	},
	"[": func(exitCode int, stdout, stderr string) ExitCodeResult {
		if exitCode >= 2 {
			return ExitCodeResult{IsError: true, Message: fmt.Sprintf("test failed with exit code %d", exitCode)}
		}
		if exitCode == 1 {
			return ExitCodeResult{IsError: false, Message: "Condition is false"}
		}
		return ExitCodeResult{IsError: false}
	},
}

// 默认退出码解释器 - 基于JavaScript版本的xQ4
var defaultExitCodeInterpreter = func(exitCode int, stdout, stderr string) ExitCodeResult {
	if exitCode != 0 {
		return ExitCodeResult{IsError: true, Message: fmt.Sprintf("Command failed with exit code %d", exitCode)}
	}
	return ExitCodeResult{IsError: false}
}

func NewBashTool(permissions core.PermissionTrigger) tools.BaseTool {
	return &bashTool{
		permissions:  permissions,
		snapshotFile: filepath.Join(utils.GetGlobalStorageDir(), "shell-snapshots", fmt.Sprintf("shell-snapshot-%d.sh", time.Now().UnixNano())),
	}
}

// generateShellSnapshot 生成shell状态快照脚本
func (t *bashTool) generateShellSnapshot() string {
	return `
# Capture shell functions
echo "# Shell Functions"
declare -f | grep -E '^[[:alnum:]_]+ \(\)' | while read line; do
    funcname=$(echo "$line" | cut -d' ' -f1)
    if [[ "$funcname" != "command_not_found_handle" ]] && [[ "$funcname" != "_"* ]]; then
        echo "declare -f $funcname" | bash
    fi
done

# Capture aliases
echo "# Shell Aliases"
alias | grep -v "^alias ls=" | grep -v "^alias ll=" | grep -v "^alias la="

# Capture shell options
echo "# Shell Options"
set +o | grep -v "set +o emacs" | grep -v "set +o vi"

# Capture environment variables (excluding system and sensitive ones)
echo "# Environment Variables"
env | grep -E '^[A-Z][A-Z0-9_]*=' | grep -vE '^(PATH|HOME|USER|SHELL|TERM|PWD|OLDPWD|SHLVL|PS1|PS2|PS3|PS4|HISTFILE|HISTSIZE|HISTCONTROL|EDITOR|VISUAL|LANG|LC_ALL|LC_CTYPE|TMPDIR|DISPLAY|SSH_|GPG_|XDG_)' | while IFS='=' read -r key value; do
    if [[ ${#key} -le 50 && ${#value} -le 500 ]]; then
        printf 'export %s=%q\n' "$key" "$value"
    fi
done

# Capture working directory
echo "# Working Directory"
echo "cd $(printf %q "$PWD")"
`
}

// createSnapshotFile 创建并锁定快照文件
func (t *bashTool) createSnapshotFile() error {
	t.snapshotMu.Lock()
	defer t.snapshotMu.Unlock()

	// 如果快照文件已存在，直接返回
	if _, err := os.Stat(t.snapshotFile); err == nil {
		return nil
	}

	// 创建快照文件目录
	dir := filepath.Dir(t.snapshotFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create snapshot directory: %v", err)
	}

	// 创建空的快照文件
	file, err := os.Create(t.snapshotFile)
	if err != nil {
		return fmt.Errorf("failed to create snapshot file: %v", err)
	}
	defer file.Close()

	// 写入初始的空状态
	_, err = file.WriteString("# QoderCLI Shell Snapshot\n# This file maintains shell state between commands\n\n")
	return err
}

// updateSnapshot 更新shell状态快照
func (t *bashTool) updateSnapshot() error {
	t.snapshotMu.Lock()
	defer t.snapshotMu.Unlock()

	// 确保快照文件存在
	if err := t.createSnapshotFile(); err != nil {
		return err
	}

	// 生成快照脚本
	snapshotScript := t.generateShellSnapshot()

	// 获取shell实例
	cwd, _ := os.Getwd()
	shellInstance := shell.GetPersistentShell(cwd)

	// 执行快照脚本获取当前状态
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	stdout, stderr, exitCode, _, err := shellInstance.Exec(ctx, snapshotScript, 5000)
	if err != nil {
		return fmt.Errorf("failed to capture shell state: %v", err)
	}

	if exitCode != 0 {
		return fmt.Errorf("snapshot script failed with exit code %d: %s", exitCode, stderr)
	}

	// 将捕获的状态写入快照文件
	return os.WriteFile(t.snapshotFile, []byte(stdout), 0644)
}

// restoreSnapshot 恢复shell状态快照
func (t *bashTool) restoreSnapshot() string {
	t.snapshotMu.Lock()
	defer t.snapshotMu.Unlock()

	// 检查快照文件是否存在
	if _, err := os.Stat(t.snapshotFile); os.IsNotExist(err) {
		return ""
	}

	// 读取快照文件内容来验证其存在且可读
	_, err := os.ReadFile(t.snapshotFile)
	if err != nil {
		return ""
	}

	// 返回source命令来恢复状态
	return fmt.Sprintf("source %s 2>/dev/null || true", shellQuote(t.snapshotFile))
}

// wrapCommandWithSnapshot 使用快照包装命令
func (t *bashTool) wrapCommandWithSnapshot(command string) string {
	// 恢复之前的状态
	restoreCmd := t.restoreSnapshot()
	if restoreCmd == "" {
		return command
	}

	// 构建完整的命令：恢复状态 + 执行命令 + 更新快照
	// 关键修复：将快照更新的输出重定向到文件，避免污染stdout
	return fmt.Sprintf(`
# Restore previous shell state
%s

# Execute the actual command
%s

# Update snapshot after execution (redirect to file to avoid stdout pollution)
(%s) > %s 2>/dev/null || true
`, restoreCmd, command, t.generateShellSnapshot(), shellQuote(t.snapshotFile))
}

// shellQuote 对shell参数进行引用转义
func shellQuote(s string) string {
	if s == "" {
		return "''"
	}

	// 如果字符串只包含安全字符，不需要引用
	if regexp.MustCompile(`^[a-zA-Z0-9_./:-]+$`).MatchString(s) {
		return s
	}

	// 转义单引号并用单引号包围
	return "'" + strings.ReplaceAll(s, "'", "'\"'\"'") + "'"
}

// cleanupSnapshot 清理快照文件
func (t *bashTool) cleanupSnapshot() {
	if t.snapshotFile != "" {
		os.Remove(t.snapshotFile)
	}
}

func (t *bashTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.BashToolName,
		Description: bashDescription,
		Parameters: map[string]any{
			"command": map[string]any{
				"type":        "string",
				"description": "The command to execute",
			},
			"timeout": map[string]any{
				"type":        "number",
				"description": "Optional timeout in milliseconds (max 600000)",
			},
			"description": map[string]any{
				"type": "string",
				"description": ` Clear, concise description of what this command does in 5-10 words. Examples:
Input: ls
Output: Lists files in current directory

Input: git status
Output: Shows working tree status

Input: npm install
Output: Installs package dependencies

Input: mkdir foo
Output: Creates directory 'foo'`,
			},
		},
		Required: []string{"command"},
	}
}

func (t *bashTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.BashSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	if params.Timeout > MaxTimeout {
		params.Timeout = MaxTimeout
	} else if params.Timeout <= 0 {
		params.Timeout = DefaultTimeout
	}

	if params.Command == "" {
		return tools.NewTextErrorResponse("missing command"), nil
	}

	// TODO 检查命令是否包含敏感操作
	// params.Command

	// 获取会话信息
	sessionId := ctx.GetSessionId()
	messageId := ctx.GetMessageId()
	if sessionId == "" || messageId == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required for creating a new file")
	}
	p := t.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionId,
			Path:        ctx.GetWorkingDir(),
			ToolName:    tools.BashToolName,
			Action:      "execute",
			Description: fmt.Sprintf("Execute command: %s", params.Command),
			Params: specs.BashPermissionsParams{
				Command: params.Command,
			},
		},
	)
	if !p {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	// 确保快照文件存在
	if err := t.createSnapshotFile(); err != nil {
		// 如果快照创建失败，记录警告但继续执行
		logging.Error("Warning: failed to create snapshot file", "err", err)
	}

	// 使用快照包装命令来保持shell状态
	wrappedCommand := t.wrapCommandWithSnapshot(params.Command)
	startTime := time.Now()

	// Try to get shell config from context
	var shellConfig *config.ShellConfig
	if shellConfigValue := ctx.GetStringConfig("shell.path", ""); shellConfigValue != "" {
		shellConfig = &config.ShellConfig{
			Path: shellConfigValue,
			Args: strings.Split(ctx.GetStringConfig("shell.args", "-l"), " "),
		}
	}

	var shellInstance *shell.PersistentShell
	if shellConfig != nil {
		shellInstance = shell.GetPersistentShellWithConfig(ctx.GetWorkingDir(), shellConfig)
	} else {
		shellInstance = shell.GetPersistentShell(ctx.GetWorkingDir())
	}

	stdout, stderr, exitCode, interrupted, err := shellInstance.Exec(ctx, wrappedCommand, params.Timeout)
	if err != nil {
		return tools.ToolResponse{}, fmt.Errorf("error executing command: %w", err)
	}

	// 退出码解释
	exitCodeResult := t.interpretExitCode(params.Command, exitCode, stdout, stderr)

	// Git操作埋点
	t.trackGitOperations(params.Command, exitCode)

	// 空白行清理、超长截断
	isImage, processedStdout := t.processContent(stdout)
	_, processedStderr := t.processContent(stderr)

	// 构建响应内容
	response := t.buildResponse(processedStdout, processedStderr, exitCode, interrupted, exitCodeResult)
	metadata := specs.BashResponseMetadata{
		StartTime:   startTime.UnixMilli(),
		EndTime:     time.Now().UnixMilli(),
		ExitCode:    exitCode,
		IsImage:     isImage,
		ExitMessage: exitCodeResult.Message,
	}
	return tools.WithResponseMetadata(tools.NewTextResponse(response), metadata), nil
}

func (t *bashTool) parseCommandType(command string) string {
	// 按|分割，取最后一个部分
	parts := strings.Split(command, "|")
	lastPart := command
	if len(parts) > 1 {
		lastPart = parts[len(parts)-1]
	}

	// 去除首尾空格
	trimmed := strings.TrimSpace(lastPart)

	// 按空白字符分割，取第一个部分
	fields := strings.Fields(trimmed)
	if len(fields) > 0 {
		return fields[0]
	}

	return ""
}

// interpretExitCode 智能解释退出码
func (t *bashTool) interpretExitCode(command string, exitCode int, stdout, stderr string) ExitCodeResult {
	// 提取命令的第一个单词来确定命令类型
	commandType := t.parseCommandType(command)
	if interpreter, exists := exitCodeInterpreters[commandType]; exists {
		return interpreter(exitCode, stdout, stderr)
	}
	return defaultExitCodeInterpreter(exitCode, stdout, stderr)
}

// trackGitOperations 追踪Git操作
func (t *bashTool) trackGitOperations(command string, exitCode int) {
	if exitCode != 0 {
		return // 只追踪成功的Git操作
	}

	// 检测git commit操作
	if matched, _ := regexp.MatchString(`^\s*git\s+commit\b`, command); matched {
		// 这里可以添加指标记录逻辑
		// E1("tengu_git_operation", {operation: "commit"})
	}

	// 检测gh pr create操作
	if matched, _ := regexp.MatchString(`^\s*gh\s+pr\s+create\b`, command); matched {
		// 这里可以添加指标记录逻辑
		// E1("tengu_git_operation", {operation: "pr_create"})
	}
}

// processContent 智能内容处理
func (t *bashTool) processContent(content string) (bool, string) {
	if content == "" {
		return false, content
	}

	// 1. 去除前后空行，注意这里不能用trim
	content = t.trimEmptyLines(content)

	// 2. 检测和处理图像内容
	if t.isImageContent(content) {
		return true, content // 图像内容不截断
	}

	// 3. 智能截断 - 基于JavaScript版本的bP
	return false, t.truncateContent(content)
}

// trimEmptyLines 去除前后空行
func (t *bashTool) trimEmptyLines(content string) string {
	lines := strings.Split(content, "\n")
	start := 0
	end := len(lines) - 1

	// 找到第一个非空行
	for start < len(lines) && strings.TrimSpace(lines[start]) == "" {
		start++
	}

	// 找到最后一个非空行
	for end >= 0 && strings.TrimSpace(lines[end]) == "" {
		end--
	}

	if start > end {
		return ""
	}

	return strings.Join(lines[start:end+1], "\n")
}

// isImageContent 检测图像内容 - 基于JavaScript版本的bP
func (t *bashTool) isImageContent(content string) bool {
	// 检测base64编码的图像数据
	matched, _ := regexp.MatchString(`^data:image/[a-z0-9.+_-]+;base64,`, content)
	return matched
}

// truncateContent 智能截断内容
func (t *bashTool) truncateContent(content string) string {
	if len(content) <= MaxOutputLength {
		return content
	}

	start := content[:MaxOutputLength]

	// 计算被截断的行数
	truncatedPart := content[MaxOutputLength:]
	truncatedLines := len(strings.Split(truncatedPart, "\n"))

	return fmt.Sprintf("%s\n\n... [%d lines truncated] ...", start, truncatedLines)
}

// buildResponse 构建最终响应 - 整合所有输出和错误信息
func (t *bashTool) buildResponse(stdout, stderr string, exitCode int, interrupted bool, exitCodeResult ExitCodeResult) string {
	var parts []string

	// 添加标准输出
	if stdout != "" {
		parts = append(parts, stdout)
	}

	// 添加标准错误
	if stderr != "" {
		parts = append(parts, strings.TrimSpace(stderr))
	}
	if exitCodeResult.IsError && exitCode != 0 {
		parts = append(parts, fmt.Sprintf("Exit code %d", exitCode))
	}

	// 添加中断信息
	if interrupted {
		parts = append(parts, "<error>Command was aborted before completion</error>")
	}

	return strings.Join(parts, "\n")
}
