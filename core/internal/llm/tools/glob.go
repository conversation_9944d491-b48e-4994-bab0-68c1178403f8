package tools

import (
	"fmt"
	"github.com/bmatcuk/doublestar/v4"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

const (
	MaxGlobLimit    = 100
	globDescription = `- Fast file pattern matching tool that works with any codebase size
- Supports glob patterns like "**/*.js" or "src/**/*.ts"
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files by name patterns
- When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead
- You have the capability to call multiple tools in a single response. It is always better to speculatively perform multiple searches as a batch that are potentially useful.`
)

type FileMatch struct {
	Path         string    `json:"path"`
	RelativePath string    `json:"relative_path"`
	Size         int64     `json:"size"`
	ModTime      time.Time `json:"mod_time"`
}

type globTool struct{}

func NewGlobTool() tools.BaseTool {
	return &globTool{}
}

func (g *globTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.GlobToolName,
		Description: globDescription,
		Parameters: map[string]any{
			"pattern": map[string]any{
				"type":        "string",
				"description": "The glob pattern to match files against",
			},
			"path": map[string]any{
				"type":        "string",
				"description": `The directory to search in. If not specified, the current working directory will be used. IMPORTANT: Omit this field to use the default directory. DO NOT enter "undefined" or "null" - simply omit it for the default behavior. Must be a valid directory path if provided.`,
			},
		},
		Required: []string{"pattern"},
	}
}

func (g *globTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	start := time.Now()
	params, err := specs.GlobSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("Invalid parameters"), nil
	}

	// Validate parameters
	if params.Pattern == "" {
		return tools.NewTextErrorResponse("Pattern is required"), nil
	}

	// Process search path
	searchPath, err := g.processSearchPath(ctx, params.Path)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("Invalid search path: %s", err)), nil
	}

	// Check if search path exists and is accessible
	if _, err := os.Stat(searchPath); err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("Cannot access search path '%s': %s", searchPath, err)), nil
	}

	// Perform glob search with enhanced features
	matches, truncated, err := g.performGlobSearch(params, searchPath, MaxGlobLimit)
	if err != nil {
		return tools.ToolResponse{}, fmt.Errorf("glob search failed: %w", err)
	}

	// Format output
	output := g.formatResults(matches, truncated)

	return tools.WithResponseMetadata(
		tools.NewTextResponse(output),
		specs.GlobResponseMetadata{
			NumberOfFiles: len(matches),
			Truncated:     truncated,
			SearchPath:    searchPath,
			Pattern:       params.Pattern,
			ElapsedMs:     time.Since(start).Milliseconds(),
		},
	), nil
}

// processSearchPath handles different path formats (absolute, home, relative)
func (g *globTool) processSearchPath(ctx tools.ToolExecutionContext, path string) (string, error) {
	if path == "" {
		return ctx.GetWorkingDir(), nil
	}

	// Handle different path formats
	if strings.HasPrefix(path, "//") {
		// Root path format //path -> /path
		return filepath.Clean(path[1:]), nil
	} else if strings.HasPrefix(path, "~/") {
		// Home directory format ~/path
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", fmt.Errorf("cannot resolve home directory: %w", err)
		}
		return filepath.Join(homeDir, path[2:]), nil
	} else if filepath.IsAbs(path) {
		// Absolute path format /path
		return filepath.Clean(path), nil
	} else {
		// Relative path format path
		workDir := ctx.GetWorkingDir()
		return filepath.Join(workDir, path), nil
	}
}

// performGlobSearch implements glob search using doublestar directly
func (g *globTool) performGlobSearch(params *specs.GlobParams, searchPath string, limit int) ([]FileMatch, bool, error) {
	fsys := os.DirFS(searchPath)
	relPattern := strings.TrimPrefix(params.Pattern, "/")
	var matches []FileMatch

	err := doublestar.GlobWalk(fsys, relPattern, func(path string, d fs.DirEntry) error {
		if d.IsDir() {
			return nil
		}

		// Skip hidden files and common ignored directories
		if g.shouldSkipFile(path) {
			return nil
		}

		info, err := d.Info()
		if err != nil {
			return nil
		}

		// Get absolute path by joining searchPath with relative path
		absPath := filepath.Join(searchPath, path)

		// Get relative path for display
		relPath, err := filepath.Rel(searchPath, absPath)
		if err != nil {
			relPath = path
		}

		matches = append(matches, FileMatch{
			Path:         absPath,
			RelativePath: relPath,
			Size:         info.Size(),
			ModTime:      info.ModTime(),
		})

		// Stop walking if we have enough results (2x limit to allow for sorting)
		if limit > 0 && len(matches) >= limit*2 {
			return fs.SkipAll
		}
		return nil
	})

	if err != nil {
		return nil, false, fmt.Errorf("glob walk error: %w", err)
	}

	// Sort by modification time (newest first)
	sort.Slice(matches, func(i, j int) bool {
		return matches[i].ModTime.After(matches[j].ModTime)
	})

	// Apply limit and check truncation
	truncated := false
	if limit > 0 && len(matches) > limit {
		matches = matches[:limit]
		truncated = true
	}

	return matches, truncated, nil
}

// shouldSkipFile checks if a file should be skipped based on common ignore patterns
func (g *globTool) shouldSkipFile(path string) bool {
	// Check for hidden files (starting with a dot)
	base := filepath.Base(path)
	if base != "." && strings.HasPrefix(base, ".") {
		return true
	}

	// Common ignored directories
	commonIgnoredDirs := map[string]bool{
		".qoder-cli":       true,
		"node_modules":     true,
		"vendor":           true,
		"dist":             true,
		"build":            true,
		"target":           true,
		".git":             true,
		".idea":            true,
		".vscode":          true,
		"__pycache__":      true,
		"bin":              true,
		"obj":              true,
		"out":              true,
		"coverage":         true,
		"tmp":              true,
		"temp":             true,
		"logs":             true,
		"generated":        true,
		"bower_components": true,
		"jspm_packages":    true,
	}

	parts := strings.Split(path, string(os.PathSeparator))
	for _, part := range parts {
		if commonIgnoredDirs[part] {
			return true
		}
	}
	return false
}

// formatResults creates human-readable output from matches
func (g *globTool) formatResults(matches []FileMatch, truncated bool) string {
	if len(matches) == 0 {
		return "No files found matching the pattern."
	}

	var output strings.Builder
	for _, match := range matches {
		// Show relative path for cleaner output
		displayPath := match.RelativePath

		// Add file info
		sizeStr := formatFileSize(match.Size)
		timeStr := match.ModTime.Format("2006-01-02 15:04")

		output.WriteString(fmt.Sprintf("%s\t%s\t%s\n", displayPath, sizeStr, timeStr))
	}

	if truncated {
		output.WriteString("\n(Results are truncated. Consider using a more specific path or pattern.)")
	}
	return output.String()
}

// formatFileSize formats file size in human-readable format
func formatFileSize(size int64) string {
	if size < 1024 {
		return fmt.Sprintf("%d B", size)
	} else if size < 1024*1024 {
		return fmt.Sprintf("%.1f KB", float64(size)/1024)
	} else if size < 1024*1024*1024 {
		return fmt.Sprintf("%.1f MB", float64(size)/(1024*1024))
	} else {
		return fmt.Sprintf("%.1f GB", float64(size)/(1024*1024*1024))
	}
}
