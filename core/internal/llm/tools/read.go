package tools

import (
	"bufio"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"io"
	"os"
	"path/filepath"
	"strings"
)

type readTool struct {
	MaxTextRead int64
	MaxTokens   int
	//lspClients map[string]*lsp.Client
}

type FileMeta struct {
	FilePath   string
	Size       int64
	Ext        string
	IsImage    bool
	IsNotebook bool
}

type FileResult interface {
	GetType() string
}

type TextFileResult struct {
	Type       string `json:"type"`
	FilePath   string `json:"file_path"`
	Content    string `json:"content"`
	NumLines   int    `json:"num_lines"`
	StartLine  int    `json:"start_line"`
	TotalLines int    `json:"total_lines"`
}

func (t *TextFileResult) GetType() string { return "text" }

type NotebookFileResult struct {
	Type     string        `json:"type"`
	FilePath string        `json:"file_path"`
	Cells    []interface{} `json:"cells"`
}

func (n *NotebookFileResult) GetType() string { return "notebook" }

type ImageFileResult struct {
	Type         string `json:"type"`
	Base64       string `json:"base64"`
	MimeType     string `json:"mime_type"`
	OriginalSize int64  `json:"original_size"`
}

func (i *ImageFileResult) GetType() string { return "image" }

var (
	imageExtensions = map[string]bool{
		"png": true, "jpg": true, "jpeg": true, "gif": true,
		"bmp": true, "webp": true,
	}

	binaryExtensions = map[string]bool{
		"mp3": true, "wav": true, "flac": true, "ogg": true, "aac": true, "m4a": true,
		"wma": true, "aiff": true, "opus": true, "mp4": true, "avi": true, "mov": true,
		"wmv": true, "flv": true, "mkv": true, "webm": true, "m4v": true, "mpeg": true,
		"mpg": true, "zip": true, "rar": true, "tar": true, "gz": true, "bz2": true,
		"7z": true, "xz": true, "z": true, "tgz": true, "iso": true, "exe": true,
		"dll": true, "so": true, "dylib": true, "app": true, "msi": true, "deb": true,
		"rpm": true, "bin": true, "dat": true, "db": true, "sqlite": true, "sqlite3": true,
		"mdb": true, "idx": true, "pdf": true, "doc": true, "docx": true, "xls": true,
		"xlsx": true, "ppt": true, "pptx": true, "odt": true, "ods": true, "odp": true,
		"ttf": true, "otf": true, "woff": true, "woff2": true, "eot": true, "psd": true,
		"ai": true, "eps": true, "sketch": true, "fig": true, "xd": true, "blend": true,
		"obj": true, "3ds": true, "max": true, "class": true, "jar": true, "war": true,
		"pyc": true, "pyo": true, "rlib": true, "swf": true, "fla": true,
	}
)

const (
	ClaudeCodeEnableUnifiedReadTool = true
	MaxTextRead                     = 256 * 1024
	MaxTokens                       = 25 * 1000
	MaxLineLength                   = 2000
	DefaultReadLimit                = 2000
	readDescriptionTemplate         = `Reads a file from the local filesystem. You can access any file directly by using this tool.
Assume this tool is able to read all files on the machine. If the User provides a path to a file assume that path is valid. It is okay to read a file that does not exist; an error will be returned.

Usage:
- The file_path parameter must be an absolute path, not a relative path
- By default, it reads up to %d lines starting from the beginning of the file
- You can optionally specify a line offset and limit (especially handy for long files), but it's recommended to read the whole file by not providing these parameters
- Any lines longer than %d characters will be truncated
- Results are returned using cat -n format, with line numbers starting at 1
- This tool allows Claude Code to read images (eg PNG, JPG, etc). When reading an image file the contents are presented visually as Claude Code is a multimodal LLM.
%s
- You have the capability to call multiple tools in a single response. It is always better to speculatively read multiple files as a batch that are potentially useful.
- You will regularly be asked to read screenshots. If the user provides a path to a screenshot ALWAYS use this tool to view the file at the path. This tool will work with all temporary file paths like /var/folders/123/abc/T/TemporaryItems/NSIRD_screencaptureui_ZfB1tD/Screenshot.png
- If you read a file that exists but has empty contents you will receive a system reminder warning in place of file contents.")
`
	textFileReadReminder = `
<system-reminder>
Whenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.
</system-reminder>
`
	textReadLimitTips                     = `File content (%s) exceeds maximum allowed size (%s). Please use offset and limit parameters to read specific portions of the file, or use the GrepTool to search for specific content.`
	tokenLimitTips                        = `File content (%d tokens) exceeds maximum allowed tokens (%d). Please use offset and limit parameters to read specific portions of the file, or use the GrepTool to search for specific content.`
	textFileReadEmptyReminder             = `<system-reminder>Warning: the file exists but the contents are empty.</system-reminder>`
	textFileReadShortReminder             = `<system-reminder>Warning: the file exists but is shorter than the provided offset (%d). The file has %d lines.</system-reminder>`
	jupyterNotebookToolDescription        = `- For Jupyter notebooks (.ipynb files), use the NotebookRead instead`
	unifiedReadJupyterNotebookDescription = `- This tool can read Jupyter notebooks (.ipynb files) and returns all cells with their outputs, combining code, text, and visualizations.`
)

func NewReadTool() tools.BaseTool {
	return &readTool{
		MaxTextRead: MaxTextRead,
		MaxTokens:   MaxTokens,
	}
}

func (v *readTool) Info() tools.ToolInfo {
	// TODO Claude Code是从环境变量中读取的配置，我们暂时不实现Notebook相关工具，默认使用Read工具读取
	description := jupyterNotebookToolDescription
	if ClaudeCodeEnableUnifiedReadTool {
		description = unifiedReadJupyterNotebookDescription
	}

	return tools.ToolInfo{
		Name:        tools.ReadToolName,
		Description: fmt.Sprintf(readDescriptionTemplate, MaxLineLength, DefaultReadLimit, description),
		Parameters: map[string]any{
			"file_path": map[string]any{
				"type":        "string",
				"description": "The absolute path to the file to read",
			},
			"offset": map[string]any{
				"type":        "integer",
				"description": "The line number to start reading from. Only provide if the file is too large to read at once",
			},
			"limit": map[string]any{
				"type":        "integer",
				"description": "The number of lines to read. Only provide if the file is too large to read at once.",
			},
		},
		Required: []string{"file_path"},
	}
}

func (v *readTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.ReadSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	meta, err := v.validateInput(ctx, params)
	if err != nil {
		return tools.NewTextErrorResponse(err.Error()), nil
	}

	// 处理Jupyter Notebook文件
	if meta.IsNotebook && ClaudeCodeEnableUnifiedReadTool {
		notebookResult, err := v.readNotebookFile(meta.FilePath)
		if err != nil {
			return tools.NewTextErrorResponse(err.Error()), nil
		}

		// TODO 定义具体的cell类型，对于连续的text cell需要做合并，现在就简单点
		text, _ := json.Marshal(notebookResult.Cells)
		recordFileRead(meta.FilePath) // 记录文件读取
		return tools.NewTextResponse(string(text)), nil
	}

	// 处理图片文件
	if meta.IsImage {
		imageResult, err := v.readImageFile(meta)
		if err != nil {
			return tools.NewTextErrorResponse(err.Error()), nil
		}
		recordFileRead(meta.FilePath) // 记录文件读取
		return tools.NewImageResponse(imageResult.MimeType, imageResult.Base64), nil
	}

	// 处理文本文件
	textResult, err := v.readTextFile(meta.FilePath, params.Offset, params.Limit)
	if err != nil {
		return tools.NewTextErrorResponse(err.Error()), nil
	}
	recordFileRead(meta.FilePath) // 记录文件读取

	responseMeta := specs.ReadResponseMetadata{
		FilePath: meta.FilePath,
		Content:  textResult.Content,
	}
	return tools.WithResponseMetadata(
		tools.NewTextResponse(formatLines(textResult.Content, textResult.StartLine)+textFileReadReminder),
		responseMeta), nil
}

func (v *readTool) validateInput(ctx tools.ToolExecutionContext, params *specs.ReadParams) (*FileMeta, error) {
	if params.FilePath == "" {
		return nil, fmt.Errorf("file_path is required")
	}

	// Get working directory from tool execution context
	workingDir := ctx.GetWorkingDir()

	// Handle relative paths
	filePath := params.FilePath
	if !filepath.IsAbs(filePath) {
		filePath = filepath.Join(workingDir, filePath)
	}
	meta := FileMeta{FilePath: filePath}

	// TODO 检测是否是被忽略的文件
	// File is in a directory that is ignored by your project configuration.

	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			// Try to offer suggestions for similarly named files
			dir := filepath.Dir(filePath)
			base := filepath.Base(filePath)

			dirEntries, dirErr := os.ReadDir(dir)
			if dirErr == nil {
				var suggestions []string
				for _, entry := range dirEntries {
					if strings.Contains(strings.ToLower(entry.Name()), strings.ToLower(base)) ||
						strings.Contains(strings.ToLower(base), strings.ToLower(entry.Name())) {
						suggestions = append(suggestions, filepath.Join(dir, entry.Name()))
						if len(suggestions) >= 3 {
							break
						}
					}
				}

				if len(suggestions) > 0 {
					// TODO 提示词不同
					return nil, fmt.Errorf("File not found: %s\n\nDid you mean one of these?\n%s",
						filePath, strings.Join(suggestions, "\n"))
				}
			}

			return nil, fmt.Errorf("file not found: %s", filePath)
		}
		return nil, fmt.Errorf("error accessing file: %w", err)
	}

	// Check if it's a directory
	if fileInfo.IsDir() {
		return nil, fmt.Errorf("path is a directory, not a file: %s", filePath)
	}

	// 检查Notebook文件
	meta.Ext = strings.ToLower(strings.TrimPrefix(filepath.Ext(filePath), "."))
	meta.IsNotebook = meta.Ext == "ipynb"
	if meta.IsNotebook && !ClaudeCodeEnableUnifiedReadTool {
		return nil, fmt.Errorf("File is a Jupyter Notebook. Use the NotebookRead to read this file.")
	}

	// 检查是否二进制
	if on, ok := binaryExtensions[meta.Ext]; on && ok {
		return nil, fmt.Errorf("This tool cannot read binary files. The file appears to be a binary %s file. Please use appropriate tools for binary file analysis.", meta.Ext)
	}

	// 检查图片文件大小
	on, ok := imageExtensions[meta.Ext]
	meta.IsImage = on && ok
	meta.Size = fileInfo.Size()
	if meta.Size == 0 && meta.IsImage {
		return nil, fmt.Errorf("Empty image files cannot be processed.")
	}

	// 检查文件大小限制（对于非图片文件、非Notebook文件）
	isReadNotebook := meta.IsNotebook && ClaudeCodeEnableUnifiedReadTool
	if !meta.IsImage && !isReadNotebook {
		if meta.Size > v.MaxTextRead && params.Offset == nil && params.Limit == nil {
			return nil, fmt.Errorf(textReadLimitTips, formatSize(meta.Size), formatSize(v.MaxTextRead))
		}
	}
	return &meta, nil
}

// readNotebookFile 读取Jupyter Notebook文件
func (v *readTool) readNotebookFile(filePath string) (*NotebookFileResult, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open notebook file: %v", err)
	}
	defer file.Close()

	// 这里简化处理，实际应该解析JSON结构
	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read notebook file: %v", err)
	}
	// fixme 这里需要解析data并且解析出cell，到一个具体的数据结构。后续渲染结果会根据cell type做展示逻辑
	// 简化的notebook处理，实际需要JSON解析
	cells := []interface{}{string(data)}

	return &NotebookFileResult{
		Type:     "notebook",
		FilePath: filePath,
		Cells:    cells,
	}, nil
}

// readTextFile 读取文本文件
func (v *readTool) readTextFile(filePath string, offset, limit *int) (*TextFileResult, error) {
	// 设置默认值
	startLine := 1
	if offset != nil {
		startLine = *offset
		if startLine < 1 {
			startLine = 1
		}
	}

	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open text file: %v", err)
	}
	defer file.Close()

	// 计算总行数
	scanner := bufio.NewScanner(file)
	var lines []string
	totalLines := 0
	for scanner.Scan() {
		totalLines++
		if limit != nil && len(lines) >= *limit {
			continue
		}

		if totalLines >= startLine {
			lines = append(lines, scanner.Text())
		}
	}

	var reminder string
	if scanner.Err() != nil {
		return nil, fmt.Errorf("failed to read file: %v", scanner.Err())
	} else if totalLines == 0 {
		reminder = textFileReadEmptyReminder
	} else if len(lines) == 0 {
		reminder = fmt.Sprintf(textFileReadShortReminder, startLine, totalLines)
	}

	// TODO content内容需要添加行号，改成 NO→Line 的格式
	content := strings.Join(lines, "\n")
	if len(reminder) > 0 {
		content += "\n" + reminder
	}
	if len(content) > int(v.MaxTokens) {
		return nil, fmt.Errorf(tokenLimitTips, len(content), v.MaxTokens)
	}

	return &TextFileResult{
		Type:       "text",
		FilePath:   filePath,
		Content:    content,
		NumLines:   len(lines),
		StartLine:  startLine,
		TotalLines: totalLines,
	}, nil
}

// readImageFile 读取图片文件
func (v *readTool) readImageFile(meta *FileMeta) (*ImageFileResult, error) {
	file, err := os.Open(meta.FilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image file: %v", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read image file: %v", err)
	}

	// Base64编码并检查编码后的大小是否超过token限制
	base64Data := base64.StdEncoding.EncodeToString(data)
	if len(base64Data) > v.MaxTokens { // TODO 这块逻辑需要再对齐一下，CC中还有图片转化的逻辑
		return nil, fmt.Errorf(tokenLimitTips, len(base64Data), v.MaxTokens)
	}

	mimeType := getMimeType(meta.Ext)
	return &ImageFileResult{
		Type:         "image",
		Base64:       base64Data,
		MimeType:     mimeType,
		OriginalSize: meta.Size,
	}, nil
}

// readLines 读取指定范围的行
func (v *readTool) readLines(filePath string, startLine int, limit *int) (string, int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", 0, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var lines []string
	lineNumber := 0

	for scanner.Scan() {
		if lineNumber >= startLine {
			lines = append(lines, scanner.Text())
			if limit != nil && len(lines) >= *limit {
				break
			}
		}
		lineNumber++
	}

	if err := scanner.Err(); err != nil {
		return "", 0, err
	}

	content := strings.Join(lines, "\n")
	return content, len(lines), nil
}

// getMimeType 根据文件扩展名获取MIME类型
func getMimeType(ext string) string {
	switch ext {
	case "jpg", "jpeg":
		return "image/jpeg"
	case "png":
		return "image/png"
	case "gif":
		return "image/gif"
	case "webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

// formatSize 格式化文件大小
func formatSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
