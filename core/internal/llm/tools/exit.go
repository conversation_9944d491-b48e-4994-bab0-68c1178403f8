package tools

import (
	"fmt"

	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
)

type exitPlanModeTool struct {
	permissions core.PermissionTrigger
}

const (
	exitPlanModeDescription = `Use this tool when you are in plan mode and have finished presenting your plan and are ready to code. This will prompt the user to exit plan mode. 
IMPORTANT: Only use this tool when the task requires planning the implementation steps of a task that requires writing code. For research tasks where you're gathering information, searching files, reading files or in general trying to understand the codebase - do NOT use this tool.

Eg. 
1. Initial task: "Search for and understand the implementation of vim mode in the codebase" - Do not use the exit plan mode tool because you are not planning the implementation steps of a task.
2. Initial task: "Help me implement yank mode for vim" - Use the exit plan mode tool after you have finished planning the implementation steps of the task.
`
)

func NewExitPlanModeTool(permissions core.PermissionTrigger) tools.BaseTool {
	return &exitPlanModeTool{
		permissions: permissions,
	}
}

func (t exitPlanModeTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.ExitPlanModeToolName,
		Description: exitPlanModeDescription,
		Parameters: map[string]any{
			"plan": map[string]any{
				"type":        "string",
				"description": "The plan you came up with, that you want to run by the user for approval. Supports markdown. The plan should be pretty concise.",
			},
		},
		Required: []string{"plan"},
	}
}

func (t exitPlanModeTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.ExitPlanModeSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse("invalid parameters"), nil
	}

	if params.Plan == "" {
		return tools.NewTextErrorResponse("plan is required"), nil
	}

	// Get session and message Id from context
	sessionID := ctx.GetSessionId()
	messageID := ctx.GetMessageId()
	if sessionID == "" || messageID == "" {
		return tools.ToolResponse{}, fmt.Errorf("session Id and message Id are required")
	}

	// Request permission from user to exit plan mode
	approved := t.permissions.CreateRequestWithContext(ctx,
		core.CreatePermissionRequest{
			SessionId:   sessionID,
			Path:        ctx.GetWorkingDir(),
			ToolName:    tools.ExitPlanModeToolName,
			Action:      "exit_plan_mode",
			Description: "Exit plan mode?",
			Params: specs.ExitPlanModePermissionParams{
				Plan: params.Plan,
			},
		},
	)

	if !approved {
		return tools.ToolResponse{}, core.ErrorPermissionDenied
	}

	// If approved, return success message guiding to start coding
	// Based on JavaScript version logic, we assume this is not an agent call
	// so we return the message for direct user interaction
	return tools.NewTextResponse("User has approved your plan. You can now start coding. Start with updating your todo list if applicable"), nil
}
