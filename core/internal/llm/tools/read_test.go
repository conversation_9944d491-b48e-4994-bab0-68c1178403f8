package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func initReadTestConfig(t *testing.T) {
}

func TestReadTool_Info(t *testing.T) {
	tool := NewReadTool()
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.ReadToolName {
		t.Errorf("Expected name %s, got %s", tools.ReadToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["file_path"]; !ok {
		t.Error("file_path parameter should be defined")
	}

	if _, ok := info.Parameters["offset"]; !ok {
		t.Error("offset parameter should be defined")
	}

	if _, ok := info.Parameters["limit"]; !ok {
		t.Error("limit parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) == 0 || info.Required[0] != "file_path" {
		t.Error("file_path should be a required parameter")
	}
}

func TestReadTool_Run_TextFile(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	// 创建测试文件
	testFile := createTestTextFile(t, "Hello World\nLine 2\nLine 3\nLine 4")
	defer os.Remove(testFile)

	params := specs.ReadParams{
		FilePath: testFile,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.Type != tools.ToolResponseTypeText {
		t.Errorf("Expected response type %s, got %s", tools.ToolResponseTypeText, response.Type)
	}

	if response.Content == "" {
		t.Error("Response content should not be empty")
	}

	// 验证包含文件内容
	if !strings.Contains(response.Content, "Hello World") {
		t.Error("Response should contain file content")
	}

	// 验证包含行号
	if !strings.Contains(response.Content, "1→") {
		t.Error("Response should contain line numbers")
	}

	// 验证包含安全提示
	if !strings.Contains(response.Content, "malicious") {
		t.Error("Response should contain security warning")
	}

	fmt.Printf("=== 文本文件读取测试输出 ===\n%s\n", response.Content)
}

func TestReadTool_Run_TextFileWithOffsetAndLimit(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	// 创建多行测试文件
	content := "Line 1\nLine 2\nLine 3\nLine 4\nLine 5\nLine 6"
	testFile := createTestTextFile(t, content)
	defer os.Remove(testFile)

	// 测试 offset 和 limit
	offset := 2
	limit := 3
	params := specs.ReadParams{
		FilePath: testFile,
		Offset:   &offset,
		Limit:    &limit,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证只包含指定行数
	if !strings.Contains(response.Content, "Line 2") {
		t.Error("Should contain Line 2")
	}

	if !strings.Contains(response.Content, "Line 4") {
		t.Error("Should contain Line 4")
	}

	if strings.Contains(response.Content, "Line 1") {
		t.Error("Should not contain Line 1 (before offset)")
	}

	if strings.Contains(response.Content, "Line 5") {
		t.Error("Should not contain Line 5 (after limit)")
	}

	fmt.Printf("=== 偏移和限制测试输出 ===\n%s\n", response.Content)
}

func TestReadTool_Run_ImageFile(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	// 创建测试图片文件（简单的PNG格式数据）
	testImageFile := createTestImageFile(t)
	defer os.Remove(testImageFile)

	params := specs.ReadParams{
		FilePath: testImageFile,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应类型为图片
	if response.Type != tools.ToolResponseTypeImage {
		t.Errorf("Expected response type %s, got %s", tools.ToolResponseTypeImage, response.Type)
	}

	if response.ImageContent == "" {
		t.Error("Image response content should not be empty")
	}

	fmt.Printf("=== 图片文件读取测试 ===\n类型: %s\n内容长度: %d\n", response.Type, len(response.Content))
}

func TestReadTool_Run_NotebookFile(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	// 创建测试 Jupyter Notebook 文件
	testNotebookFile := createTestNotebookFile(t)
	defer os.Remove(testNotebookFile)

	params := specs.ReadParams{
		FilePath: testNotebookFile,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.Type != tools.ToolResponseTypeText {
		t.Errorf("Expected response type %s, got %s", tools.ToolResponseTypeText, response.Type)
	}

	if response.Content == "" {
		t.Error("Notebook response content should not be empty")
	}

	// 验证包含 notebook 结构
	if !strings.Contains(response.Content, "cells") {
		t.Error("Notebook response should contain cells")
	}

	fmt.Printf("=== Notebook文件读取测试输出 ===\n%s\n", response.Content)
}

func TestReadTool_ValidateInput(t *testing.T) {
	tool := &readTool{
		MaxTextRead: MaxTextRead,
		MaxTokens:   MaxTokens,
	}
	initReadTestConfig(t)

	tests := []struct {
		name    string
		params  specs.ReadParams
		wantErr bool
		errMsg  string
	}{
		{
			name:    "空文件路径",
			params:  specs.ReadParams{FilePath: ""},
			wantErr: true,
			errMsg:  "file_path is required",
		},
		{
			name:    "不存在的文件",
			params:  specs.ReadParams{FilePath: "./nonexistent.txt"},
			wantErr: true,
			errMsg:  "file not found",
		},
		{
			name:    "目录而非文件",
			params:  specs.ReadParams{FilePath: "."},
			wantErr: true,
			errMsg:  "path is a directory",
		},
		{
			name:    "二进制文件",
			params:  specs.ReadParams{FilePath: createTestBinaryFile(t)},
			wantErr: true,
			errMsg:  "binary file",
		},
		{
			name:    "有效文本文件",
			params:  specs.ReadParams{FilePath: createTestTextFile(t, "test content")},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
			_, err := tool.validateInput(toolCtx, &tt.params)

			// 清理测试文件
			if tt.params.FilePath != "" && tt.params.FilePath != "." && !strings.Contains(tt.params.FilePath, "nonexistent") {
				defer os.Remove(tt.params.FilePath)
			}

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
				} else if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("Expected error containing '%s', got '%s'", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

func TestReadTool_Run_ErrorHandling(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name:    "无效JSON",
			input:   "invalid json",
			wantErr: false, // 应该返回错误响应
		},
		{
			name:    "缺少文件路径",
			input:   `{}`,
			wantErr: false, // 应该返回错误响应
		},
		{
			name:    "不存在的文件",
			input:   `{"file_path": "./nonexistent.txt"}`,
			wantErr: false, // 应该返回错误响应
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			call := tools.ToolCall{Input: tt.input}
			response, err := tool.Run(toolCtx, call)

			if tt.wantErr && err == nil {
				t.Error("Expected error but got none")
			}

			if !tt.wantErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// 对于错误情况，应该返回错误响应
			if tt.input == "invalid json" || strings.Contains(tt.input, "nonexistent") || tt.input == `{}` {
				if response.Type != tools.ToolResponseTypeText {
					t.Error("Should return text response for error cases")
				}
				if !response.IsError {
					t.Error("Error response should be marked as error")
				}
			}
		})
	}
}

func TestReadTool_Run_EmptyFile(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	// 创建空文件
	testFile := createTestTextFile(t, "")
	defer os.Remove(testFile)

	params := specs.ReadParams{
		FilePath: testFile,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证空文件的提示
	if !strings.Contains(response.Content, "empty") {
		t.Error("Should contain empty file warning")
	}

	fmt.Printf("=== 空文件测试输出 ===\n%s\n", response.Content)
}

func TestReadTool_ReadLines(t *testing.T) {
	tool := &readTool{
		MaxTextRead: MaxTextRead,
		MaxTokens:   MaxTokens,
	}
	initReadTestConfig(t)

	// 创建测试文件
	content := "Line 1\nLine 2\nLine 3\nLine 4\nLine 5"
	testFile := createTestTextFile(t, content)
	defer os.Remove(testFile)

	tests := []struct {
		name      string
		startLine int
		limit     *int
		expected  []string
	}{
		{
			name:      "从头读取所有行",
			startLine: 0,
			limit:     nil,
			expected:  []string{"Line 1", "Line 2", "Line 3", "Line 4", "Line 5"},
		},
		{
			name:      "从第二行开始读取",
			startLine: 1,
			limit:     nil,
			expected:  []string{"Line 2", "Line 3", "Line 4", "Line 5"},
		},
		{
			name:      "读取前3行",
			startLine: 0,
			limit:     intPtr(3),
			expected:  []string{"Line 1", "Line 2", "Line 3"},
		},
		{
			name:      "从第3行开始读取2行",
			startLine: 2,
			limit:     intPtr(2),
			expected:  []string{"Line 3", "Line 4"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, numLines, err := tool.readLines(testFile, tt.startLine, tt.limit)
			if err != nil {
				t.Fatalf("Failed to read lines: %v", err)
			}

			lines := strings.Split(result, "\n")
			if len(lines) != len(tt.expected) {
				t.Errorf("Expected %d lines, got %d", len(tt.expected), len(lines))
			}

			if numLines != len(tt.expected) {
				t.Errorf("Expected numLines %d, got %d", len(tt.expected), numLines)
			}

			for i, expected := range tt.expected {
				if i < len(lines) && lines[i] != expected {
					t.Errorf("Line %d: expected '%s', got '%s'", i, expected, lines[i])
				}
			}
		})
	}
}

func TestReadTool_FormatSize(t *testing.T) {
	tests := []struct {
		size     int64
		expected string
	}{
		{0, "0 B"},
		{1023, "1023 B"},
		{1024, "1.0 KB"},
		{1536, "1.5 KB"},
		{1048576, "1.0 MB"},
		{1073741824, "1.0 GB"},
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("size_%d", tt.size), func(t *testing.T) {
			result := formatSize(tt.size)
			if result != tt.expected {
				t.Errorf("formatSize(%d) = %s, want %s", tt.size, result, tt.expected)
			}
		})
	}
}

func TestReadTool_GetMimeType(t *testing.T) {
	tests := []struct {
		ext      string
		expected string
	}{
		{"jpg", "image/jpeg"},
		{"jpeg", "image/jpeg"},
		{"png", "image/png"},
		{"gif", "image/gif"},
		{"webp", "image/webp"},
		{"unknown", "application/octet-stream"},
	}

	for _, tt := range tests {
		t.Run(tt.ext, func(t *testing.T) {
			result := getMimeType(tt.ext)
			if result != tt.expected {
				t.Errorf("getMimeType(%s) = %s, want %s", tt.ext, result, tt.expected)
			}
		})
	}
}

// 辅助函数
func createTestTextFile(t *testing.T, content string) string {
	tmpFile, err := os.CreateTemp("", "test_text_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	if _, err := tmpFile.WriteString(content); err != nil {
		t.Fatalf("Failed to write content: %v", err)
	}

	tmpFile.Close()
	return tmpFile.Name()
}

func createTestImageFile(t *testing.T) string {
	tmpFile, err := os.CreateTemp("", "test_image_*.png")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	// 创建一个最小的PNG文件头
	pngHeader := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
	if _, err := tmpFile.Write(pngHeader); err != nil {
		t.Fatalf("Failed to write PNG header: %v", err)
	}

	tmpFile.Close()
	return tmpFile.Name()
}

func createTestNotebookFile(t *testing.T) string {
	tmpFile, err := os.CreateTemp("", "test_notebook_*.ipynb")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	// 创建简单的 notebook JSON 结构
	notebook := `{
  "cells": [
    {
      "cell_type": "code",
      "source": ["print('Hello World')"],
      "outputs": []
    },
    {
      "cell_type": "markdown",
      "source": ["# This is a test notebook"]
    }
  ],
  "metadata": {},
  "nbformat": 4,
  "nbformat_minor": 4
}`

	if _, err := tmpFile.WriteString(notebook); err != nil {
		t.Fatalf("Failed to write notebook content: %v", err)
	}

	tmpFile.Close()
	return tmpFile.Name()
}

func createTestBinaryFile(t *testing.T) string {
	tmpFile, err := os.CreateTemp("", "test_binary_*.exe")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	// 写入一些二进制数据
	binaryData := []byte{0x4D, 0x5A, 0x90, 0x00, 0x03, 0x00} // DOS header
	if _, err := tmpFile.Write(binaryData); err != nil {
		t.Fatalf("Failed to write binary data: %v", err)
	}

	tmpFile.Close()
	return tmpFile.Name()
}

func intPtr(i int) *int {
	return &i
}

// 基准测试

func BenchmarkReadTool_Run(b *testing.B) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()

	// 创建测试文件
	content := strings.Repeat("This is a test line.\n", 100)
	tmpFile, _ := os.CreateTemp("", "bench_test_*.txt")
	tmpFile.WriteString(content)
	tmpFile.Close()
	defer os.Remove(tmpFile.Name())

	params := specs.ReadParams{FilePath: tmpFile.Name()}
	input, _ := json.Marshal(params)
	call := tools.ToolCall{Input: string(input)}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := tool.Run(toolCtx, call)
		if err != nil {
			b.Fatalf("Failed to run tool: %v", err)
		}
	}
}

func BenchmarkReadTool_ReadLines(b *testing.B) {
	tool := &readTool{
		MaxTextRead: MaxTextRead,
		MaxTokens:   MaxTokens,
	}

	// 创建大文件
	content := strings.Repeat("This is a test line.\n", 1000)
	tmpFile, _ := os.CreateTemp("", "bench_test_*.txt")
	tmpFile.WriteString(content)
	tmpFile.Close()
	defer os.Remove(tmpFile.Name())

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := tool.readLines(tmpFile.Name(), 0, nil)
		if err != nil {
			b.Fatalf("Failed to read lines: %v", err)
		}
	}
}

// TestReadTool_RecordFileRead 测试读取文件时是否正确记录文件读取
func TestReadTool_RecordFileRead(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	t.Run("文本文件读取记录", func(t *testing.T) {
		// 创建测试文件
		testFile := createTestTextFile(t, "Hello World\nLine 2")
		defer os.Remove(testFile)

		// 获取绝对路径
		absPath, _ := filepath.Abs(testFile)

		// 验证读取前没有记录
		readTime := getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("文件读取前不应该有读取记录")
		}

		// 执行读取
		params := specs.ReadParams{FilePath: testFile}
		input, _ := json.Marshal(params)
		call := tools.ToolCall{Input: string(input)}

		response, err := tool.Run(toolCtx, call)
		if err != nil {
			t.Fatalf("读取文件失败: %v", err)
		}

		if response.IsError {
			t.Fatalf("读取文件返回错误: %s", response.Content)
		}

		// 验证读取后有记录
		readTime = getLastReadTime(absPath)
		if readTime.IsZero() {
			t.Error("文件读取后应该有读取记录")
		}

		// 验证记录时间是最近的
		if time.Since(readTime) > time.Second {
			t.Error("读取时间记录不正确，应该是最近的时间")
		}
	})

	t.Run("图片文件读取记录", func(t *testing.T) {
		// 创建测试图片文件
		testImageFile := createTestImageFile(t)
		defer os.Remove(testImageFile)

		// 获取绝对路径
		absPath, _ := filepath.Abs(testImageFile)

		// 验证读取前没有记录
		readTime := getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("图片文件读取前不应该有读取记录")
		}

		// 执行读取
		params := specs.ReadParams{FilePath: testImageFile}
		input, _ := json.Marshal(params)
		call := tools.ToolCall{Input: string(input)}

		response, err := tool.Run(toolCtx, call)
		if err != nil {
			t.Fatalf("读取图片文件失败: %v", err)
		}

		if response.IsError {
			t.Fatalf("读取图片文件返回错误: %s", response.Content)
		}

		// 验证读取后有记录
		readTime = getLastReadTime(absPath)
		if readTime.IsZero() {
			t.Error("图片文件读取后应该有读取记录")
		}

		// 验证记录时间是最近的
		if time.Since(readTime) > time.Second {
			t.Error("图片读取时间记录不正确，应该是最近的时间")
		}
	})

	t.Run("Notebook文件读取记录", func(t *testing.T) {
		// 创建测试notebook文件
		testNotebookFile := createTestNotebookFile(t)
		defer os.Remove(testNotebookFile)

		// 获取绝对路径
		absPath, _ := filepath.Abs(testNotebookFile)

		// 验证读取前没有记录
		readTime := getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("Notebook文件读取前不应该有读取记录")
		}

		// 执行读取
		params := specs.ReadParams{FilePath: testNotebookFile}
		input, _ := json.Marshal(params)
		call := tools.ToolCall{Input: string(input)}

		response, err := tool.Run(toolCtx, call)
		if err != nil {
			t.Fatalf("读取Notebook文件失败: %v", err)
		}

		if response.IsError {
			t.Fatalf("读取Notebook文件返回错误: %s", response.Content)
		}

		// 验证读取后有记录
		readTime = getLastReadTime(absPath)
		if readTime.IsZero() {
			t.Error("Notebook文件读取后应该有读取记录")
		}

		// 验证记录时间是最近的
		if time.Since(readTime) > time.Second {
			t.Error("Notebook读取时间记录不正确，应该是最近的时间")
		}
	})
}

// TestReadTool_RecordFileRead_ErrorCases 测试错误情况下不应该记录文件读取
func TestReadTool_RecordFileRead_ErrorCases(t *testing.T) {
	tool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	t.Run("文件不存在时不记录", func(t *testing.T) {
		nonExistentFile := "./nonexistent_file.txt"
		absPath, _ := filepath.Abs(nonExistentFile)

		// 验证读取前没有记录
		readTime := getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("不存在文件读取前不应该有读取记录")
		}

		// 尝试读取不存在的文件
		params := specs.ReadParams{FilePath: nonExistentFile}
		input, _ := json.Marshal(params)
		call := tools.ToolCall{Input: string(input)}

		response, err := tool.Run(toolCtx, call)
		if err != nil {
			t.Fatalf("工具运行失败: %v", err)
		}

		// 应该返回错误响应
		if !response.IsError {
			t.Error("读取不存在文件应该返回错误")
		}

		// 验证失败后仍然没有记录
		readTime = getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("读取失败后不应该有读取记录")
		}
	})

	t.Run("二进制文件错误时不记录", func(t *testing.T) {
		// 创建二进制文件
		binaryFile := createTestBinaryFile(t)
		defer os.Remove(binaryFile)
		absPath, _ := filepath.Abs(binaryFile)

		// 验证读取前没有记录
		readTime := getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("二进制文件读取前不应该有读取记录")
		}

		// 尝试读取二进制文件
		params := specs.ReadParams{FilePath: binaryFile}
		input, _ := json.Marshal(params)
		call := tools.ToolCall{Input: string(input)}

		response, err := tool.Run(toolCtx, call)
		if err != nil {
			t.Fatalf("工具运行失败: %v", err)
		}

		// 应该返回错误响应
		if !response.IsError {
			t.Error("读取二进制文件应该返回错误")
		}

		// 验证失败后仍然没有记录
		readTime = getLastReadTime(absPath)
		if !readTime.IsZero() {
			t.Error("读取二进制文件失败后不应该有读取记录")
		}
	})
}

// TestReadTool_Integration_WithEditTool 集成测试：验证read工具的记录与edit工具的检查
func TestReadTool_Integration_WithEditTool(t *testing.T) {
	readTool := NewReadTool()
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	initReadTestConfig(t)

	// 创建测试文件
	originalContent := "Hello World\nLine 2"
	testFile := createTestTextFile(t, originalContent)
	defer os.Remove(testFile)

	absPath, _ := filepath.Abs(testFile)

	// 1. 模拟edit工具的检查 - 文件未读取时应该失败
	editParams := specs.EditParams{
		FilePath:  absPath,
		OldString: "Hello World",
		NewString: "Hello Universe",
	}

	response := Validate(&editParams)
	// 文件未读取前应该返回错误
	if !response.IsError {
		t.Error("文件未读取前edit检查应该失败")
	}
	if !strings.Contains(response.Content, "has not been read yet") {
		t.Errorf("错误消息应该包含'has not been read yet'，实际: %s", response.Content)
	}

	// 2. 使用read工具读取文件
	readParams := specs.ReadParams{FilePath: testFile}
	input, _ := json.Marshal(readParams)
	call := tools.ToolCall{Input: string(input)}

	readResponse, err := readTool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("读取文件失败: %v", err)
	}

	if readResponse.IsError {
		t.Fatalf("读取文件返回错误: %s", readResponse.Content)
	}

	// 3. 再次检查edit工具 - 现在应该通过
	response = Validate(&editParams)
	// 文件读取后应该可以进行编辑
	if response.IsError {
		t.Errorf("文件读取后edit检查应该成功，但得到错误: %s", response.Content)
	}

	fmt.Printf("=== 集成测试成功 ===\n读取工具响应: %v\n编辑检查结果: 通过\n", len(readResponse.Content) > 0)
}

// TestFileRecord_ConcurrentAccess 测试并发访问文件记录
func TestFileRecord_ConcurrentAccess(t *testing.T) {
	initReadTestConfig(t)

	// 创建测试文件
	testFile := createTestTextFile(t, "concurrent test")
	defer os.Remove(testFile)
	absPath, _ := filepath.Abs(testFile)

	// 并发执行读取记录
	const numGoroutines = 10
	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			recordFileRead(absPath)
			done <- true
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// 验证记录存在
	readTime := getLastReadTime(absPath)
	if readTime.IsZero() {
		t.Error("并发读取后应该有读取记录")
	}

	// 验证记录时间是最近的
	if time.Since(readTime) > time.Second {
		t.Error("并发读取时间记录不正确")
	}
}
