package tools

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/utils/diff"
)

type writeTool struct {
	//lspClients  map[string]*lsp.Client
	permissions core.PermissionTrigger
	files       core.HistoryService
}

const (
	writeDescription = `Writes a file to the local filesystem.

Usage:
- This tool will overwrite the existing file if there is one at the provided path.
- If this is an existing file, you MUST use the Read tool first to read the file's contents. This tool will fail if you did not read the file first.
- ALWAYS prefer editing existing files in the codebase. NEVER write new files unless explicitly required.
- NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
- Only use emojis if the user explicitly requests it. Avoid writing emojis to files unless asked.`
)

func NewWriteTool(permissions core.PermissionTrigger, files core.HistoryService) tools.BaseTool {
	return &writeTool{
		permissions: permissions,
		files:       files,
	}
}

func (w *writeTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.WriteToolName,
		Description: writeDescription,
		Parameters: map[string]any{
			"file_path": map[string]any{
				"type":        "string",
				"description": "The absolute path to the file to write (must be absolute, not relative)",
			},
			"content": map[string]any{
				"type":        "string",
				"description": "The content to write to the file",
			},
		},
		Required: []string{"file_path", "content"},
	}
}

func (w *writeTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.WriteSpec.GetParams(call.Input)
	if err := json.Unmarshal([]byte(call.Input), &params); err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	if params.FilePath == "" {
		return tools.NewTextErrorResponse("file_path is required"), nil
	}

	if params.Content == "" {
		return tools.NewTextErrorResponse("content is required"), nil
	}

	// 处理文件路径为绝对路径
	filePath := w.resolveFilePath(ctx, params.FilePath)

	// TODO 校验是否为忽略的文件
	// File is in a directory that is ignored by your project configuration.

	// 先检查是否是目录（在文件访问验证之前）
	fileInfo, err := os.Stat(filePath)
	if err == nil && fileInfo.IsDir() {
		return tools.NewTextErrorResponse(fmt.Sprintf("Path is a directory, not a file: %s", filePath)), nil
	}

	// 检查文件状态和读取要求
	if err := w.validateFileAccess(filePath); err != nil {
		return tools.NewTextErrorResponse(err.Error()), nil
	}

	// 解析符号链接
	resolvedPath, err := w.resolveSymlink(filePath)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error resolving symlink: %s", err)), nil
	}

	// 读取现有文件内容和元数据
	oldContent, encoding, lineEnding, fileExists, err := w.readFileWithMetadata(resolvedPath)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error reading existing file: %s", err)), nil
	}

	// 检查内容是否相同
	if fileExists && oldContent == params.Content {
		return tools.NewTextErrorResponse(fmt.Sprintf("File %s already contains the exact content. No changes made.", filePath)), nil
	}

	// 获取会话信息
	sessionID := ctx.GetSessionId()
	messageID := ctx.GetMessageId()
	if sessionID == "" || messageID == "" {
		return tools.ToolResponse{}, fmt.Errorf("session_id and message_id are required")
	}

	// 生成diff
	diffContent, additions, removals := diff.GenerateDiff(oldContent, params.Content, filePath, ctx.GetWorkingDir())

	// 请求权限
	if err := w.requestPermission(ctx, sessionID, filePath, diffContent); err != nil {
		return tools.ToolResponse{}, err
	}

	// 处理行尾符
	processedContent := w.processLineEndings(params.Content, lineEnding)

	// 原子化写入文件
	atomicWrite, err := w.writeFileAtomically(resolvedPath, processedContent, encoding)
	if err != nil {
		return tools.ToolResponse{}, fmt.Errorf("error writing file: %w", err)
	}

	// 更新文件历史
	if err := w.updateFileHistory(ctx, sessionID, filePath, oldContent, params.Content, fileExists); err != nil {
		logging.Debug("Error updating file history", "error", err)
	}

	// 记录文件访问
	recordFileWrite(filePath)
	recordFileRead(filePath)

	// 等待LSP诊断
	//if w.lspClients != nil {
	//	waitForLspDiagnostics(ctx, filePath, w.lspClients)
	//}

	// 记录统计信息
	w.recordFileChangeStats(additions, removals)

	// 准备响应
	operationType := "create"
	if fileExists {
		operationType = "update"
	}

	result := fmt.Sprintf("File successfully %s: %s",
		map[string]string{"create": "created", "update": "updated"}[operationType],
		filePath)

	//if w.lspClients != nil {
	//	result = fmt.Sprintf("<result>\n%s\n</result>", result)
	//	result += getDiagnostics(filePath, w.lspClients)
	//}

	return tools.WithResponseMetadata(tools.NewTextResponse(result), specs.WriteResponseMetadata{
		Type:        operationType,
		FilePath:    filePath,
		Diff:        diffContent,
		Additions:   additions,
		Removals:    removals,
		Encoding:    encoding,
		LineEnding:  lineEnding,
		AtomicWrite: atomicWrite,
	}), nil
}

// resolveFilePath 将路径转换为绝对路径
func (w *writeTool) resolveFilePath(ctx tools.ToolExecutionContext, filePath string) string {
	if filepath.IsAbs(filePath) {
		return filePath
	}
	// Get working directory from tool execution context
	workingDir := ctx.GetWorkingDir()
	return filepath.Join(workingDir, filePath)
}

// validateFileAccess 验证文件访问权限和读取状态
func (w *writeTool) validateFileAccess(filePath string) error {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // 新文件，允许创建
		}
		return fmt.Errorf("error checking file: %w", err)
	}

	// 检查文件是否被Read工具读取过
	lastRead := getLastReadTime(filePath)
	if lastRead.IsZero() {
		return fmt.Errorf("File has not been read yet. Read it first before writing to it.")
	}

	// 检查是否已读取过文件
	modTime := fileInfo.ModTime()
	if modTime.After(lastRead) {
		return fmt.Errorf("File %s has been modified since it was last read.\nLast modification: %s\nLast read: %s\n\nPlease read the file again before modifying it.",
			filePath, modTime.Format(time.RFC3339), lastRead.Format(time.RFC3339))
	}

	return nil
}

// resolveSymlink 解析符号链接
func (w *writeTool) resolveSymlink(filePath string) (string, error) {
	// 检查是否是符号链接
	linkInfo, err := os.Lstat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return filePath, nil // 文件不存在，返回原路径
		}
		return "", err
	}

	if linkInfo.Mode()&os.ModeSymlink == 0 {
		return filePath, nil // 不是符号链接
	}

	// 解析符号链接
	target, err := os.Readlink(filePath)
	if err != nil {
		return "", err
	}

	// 如果是相对路径，转换为绝对路径
	if !filepath.IsAbs(target) {
		target = filepath.Join(filepath.Dir(filePath), target)
	}

	logging.Debug("Target is a symlink pointing to", "target", target)
	return target, nil
}

// detectFileEncoding 检测文件编码
func (w *writeTool) detectFileEncoding(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "utf-8", nil // 默认使用UTF-8
	}
	defer file.Close()

	// 读取前4096字节用于检测编码
	buffer := make([]byte, 4096)
	n, err := file.Read(buffer)
	if err != nil && n == 0 {
		return "utf-8", nil
	}

	buffer = buffer[:n]

	// 检测BOM
	if n >= 3 && buffer[0] == 0xEF && buffer[1] == 0xBB && buffer[2] == 0xBF {
		return "utf-8", nil // UTF-8 BOM
	}
	if n >= 2 && buffer[0] == 0xFF && buffer[1] == 0xFE {
		return "utf-16le", nil // UTF-16 LE BOM
	}

	// 检查是否是有效的UTF-8
	if utf8.Valid(buffer) {
		return "utf-8", nil
	}

	return "ascii", nil
}

// detectLineEnding 检测行尾符类型
func (w *writeTool) detectLineEnding(content string) string {
	crlfCount := 0
	lfCount := 0

	for i := 0; i < len(content); i++ {
		if content[i] == '\n' {
			if i > 0 && content[i-1] == '\r' {
				crlfCount++
			} else {
				lfCount++
			}
		}
	}

	if crlfCount > lfCount {
		return "CRLF"
	}
	return "LF"
}

// getDefaultLineEnding 获取项目默认行尾符
func (w *writeTool) getDefaultLineEnding() string {
	// 简化实现，可以根据项目配置或操作系统决定
	return "LF"
}

// readFileWithMetadata 读取文件内容及其元数据
func (w *writeTool) readFileWithMetadata(filePath string) (content, encoding, lineEnding string, exists bool, err error) {
	_, err = os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return "", "utf-8", w.getDefaultLineEnding(), false, nil
		}
		return "", "", "", false, err
	}

	// 检测编码
	encoding, err = w.detectFileEncoding(filePath)
	if err != nil {
		return "", "", "", false, err
	}

	// 读取文件内容
	contentBytes, err := os.ReadFile(filePath)
	if err != nil {
		return "", "", "", false, err
	}

	content = string(contentBytes)
	lineEnding = w.detectLineEnding(content)

	return content, encoding, lineEnding, true, nil
}

// processLineEndings 处理行尾符转换
func (w *writeTool) processLineEndings(content, targetLineEnding string) string {
	if targetLineEnding == "CRLF" {
		// 将LF转换为CRLF
		return strings.ReplaceAll(content, "\n", "\r\n")
	}
	return content // LF是默认格式，不需要转换
}

// requestPermission 请求写入权限
func (w *writeTool) requestPermission(ctx tools.ToolExecutionContext, sessionID, filePath, diffContent string) error {
	// Get root directory from tool execution context
	rootDir := ctx.GetWorkingDir()
	permissionPath := filepath.Dir(filePath)
	if strings.HasPrefix(filePath, rootDir) {
		permissionPath = rootDir
	}

	approved := w.permissions.CreateRequestWithContext(ctx, core.CreatePermissionRequest{
		SessionId:   sessionID,
		Path:        permissionPath,
		ToolName:    tools.WriteToolName,
		Action:      "write",
		Description: fmt.Sprintf("Write to file %s", filePath),
		Params: specs.WritePermissionsParams{
			FilePath: filePath,
			Diff:     diffContent,
		},
	})

	if !approved {
		return core.ErrorPermissionDenied
	}

	return nil
}

// writeFileAtomically 原子化写入文件
func (w *writeTool) writeFileAtomically(filePath, content, encoding string) (bool, error) {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return false, fmt.Errorf("error creating directory: %w", err)
	}

	// 获取原始文件权限
	var originalMode os.FileMode = 0o644
	if fileInfo, err := os.Stat(filePath); err == nil {
		originalMode = fileInfo.Mode()
	}

	// 创建临时文件名
	tempFile := fmt.Sprintf("%s.tmp.%d.%d", filePath, os.Getpid(), time.Now().UnixNano())

	// 尝试原子化写入
	err := func() error {
		// 写入临时文件
		if err := os.WriteFile(tempFile, []byte(content), originalMode); err != nil {
			return err
		}

		// 重命名临时文件到目标文件（原子操作）
		return os.Rename(tempFile, filePath)
	}()

	if err != nil {
		// 清理临时文件
		os.Remove(tempFile)

		// 回退到直接写入
		logging.Debug("Atomic write failed, falling back to direct write", "error", err)
		if writeErr := os.WriteFile(filePath, []byte(content), originalMode); writeErr != nil {
			return false, writeErr
		}
		return false, nil
	}

	logging.Debug("File written atomically", "path", filePath)
	return true, nil
}

// updateFileHistory 更新文件历史记录
func (w *writeTool) updateFileHistory(ctx tools.ToolExecutionContext, sessionID, filePath, oldContent, newContent string, fileExists bool) error {
	if !fileExists {
		// 创建新文件历史记录，使用实际的初始内容（通常为空字符串，但这是有意义的初始状态）
		_, err := w.files.Create(ctx, sessionID, filePath, oldContent)
		if err != nil {
			return err
		}
	} else {
		// 检查现有历史记录
		file, err := w.files.GetByPathAndSession(ctx, filePath, sessionID)
		if err != nil {
			// 创建历史记录
			_, err = w.files.Create(ctx, sessionID, filePath, oldContent)
			if err != nil {
				return err
			}
		} else if file.Content != oldContent {
			// 用户手动修改了内容，存储中间版本
			_, err = w.files.CreateVersion(ctx, sessionID, filePath, oldContent)
			if err != nil {
				logging.Debug("Error creating intermediate file version", "error", err)
			}
		}
	}

	// 存储新版本
	_, err := w.files.CreateVersion(ctx, sessionID, filePath, newContent)
	return err
}

// recordFileChangeStats 记录文件变更统计
func (w *writeTool) recordFileChangeStats(additions, removals int) {
	// 记录统计信息（简化实现）
	logging.Debug("File change statistics",
		"additions", additions,
		"removals", removals)

	// 可以在这里添加更多的统计记录逻辑
	// 例如：发送到监控系统、更新全局计数器等
}
