package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestGlobTool_Info(t *testing.T) {
	tool := NewGlobTool()
	info := tool.Info()

	// Test basic info
	if info.Name != tools.GlobToolName {
		t.Errorf("expected name %s, got %s", tools.GlobToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("expected non-empty description")
	}

	// Test required parameters
	if len(info.Required) != 1 || info.Required[0] != "pattern" {
		t.Errorf("expected required=['pattern'], got %v", info.Required)
	}

	// Test parameter definitions
	expectedParams := []string{"pattern", "path"}
	for _, param := range expectedParams {
		if _, exists := info.Parameters[param]; !exists {
			t.<PERSON>rrorf("missing parameter definition: %s", param)
		}
	}
}

func TestGlobTool_Run_ParameterValidation(t *testing.T) {
	tool := NewGlobTool()

	tests := []struct {
		name        string
		input       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "empty pattern",
			input:       `{"pattern": ""}`,
			expectError: true,
			errorMsg:    "Pattern is required",
		},
		{
			name:        "missing pattern",
			input:       `{"path": "/tmp"}`,
			expectError: true,
			errorMsg:    "Pattern is required",
		},
		{
			name:        "invalid json",
			input:       `{"pattern": "*.go"`,
			expectError: true,
			errorMsg:    "Invalid parameters",
		},
		{
			name:        "valid pattern only",
			input:       `{"pattern": "*.go"}`,
			expectError: false,
		},
		{
			name:        "valid pattern with path",
			input:       `{"pattern": "*.go", "path": "."}`,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Initialize config only for tests that actually run the tool
			if !tt.expectError {
				_ = initGlobTestConfig(t)
			}

			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
			call := tools.ToolCall{Input: tt.input}

			response, err := tool.Run(toolCtx, call)

			if tt.expectError {
				if err != nil {
					t.Errorf("expected error response, got actual error: %v", err)
				}
				if response.Type != tools.ToolResponseTypeText || !strings.Contains(response.Content, tt.errorMsg) {
					t.Errorf("expected error message containing '%s', got: %s", tt.errorMsg, response.Content)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if response.Type != tools.ToolResponseTypeText {
					t.Errorf("expected text response, got: %s", response.Type)
				}
			}
		})
	}
}

func TestGlobTool_ProcessSearchPath(t *testing.T) {
	// Initialize config to avoid panic
	testDir := initGlobTestConfig(t)

	tool := &globTool{}

	// Get home directory for tests
	homeDir, _ := os.UserHomeDir()

	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:     "empty path uses working directory",
			input:    "",
			expected: testDir,
		},
		{
			name:     "root path format",
			input:    "//usr/local",
			expected: "/usr/local",
		},
		{
			name:     "home directory format",
			input:    "~/Documents",
			expected: filepath.Join(homeDir, "Documents"),
		},
		{
			name:     "absolute path",
			input:    "/tmp/test",
			expected: "/tmp/test",
		},
		{
			name:     "relative path",
			input:    "src/main",
			expected: filepath.Join(testDir, "src/main"),
		},
		{
			name:     "current directory",
			input:    ".",
			expected: testDir,
		},
		{
			name:     "parent directory",
			input:    "..",
			expected: filepath.Join(testDir, ".."),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
				WithWorkingDir(testDir).
				Build()
			result, err := tool.processSearchPath(toolCtx, tt.input)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}

				expected := filepath.Clean(tt.expected)
				result = filepath.Clean(result)

				if result != expected {
					t.Errorf("expected %s, got %s", expected, result)
				}
			}
		})
	}
}

func TestGlobTool_FormatFileSize(t *testing.T) {
	tests := []struct {
		size     int64
		expected string
	}{
		{0, "0 B"},
		{512, "512 B"},
		{1024, "1.0 KB"},
		{1536, "1.5 KB"},
		{1048576, "1.0 MB"},
		{1572864, "1.5 MB"},
		{1073741824, "1.0 GB"},
		{1610612736, "1.5 GB"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := formatFileSize(tt.size)
			if result != tt.expected {
				t.Errorf("formatFileSize(%d) = %s, want %s", tt.size, result, tt.expected)
			}
		})
	}
}

func TestGlobTool_Run_WithTestDirectory(t *testing.T) {
	// Create test directory structure
	testDir := initGlobTestConfig(t)
	tool := NewGlobTool()

	// Create test files
	testFiles := []string{
		"test1.go",
		"test2.txt",
		"subdir/test3.go",
		"subdir/test4.txt",
		"node_modules/package.json", // Should be ignored
		".git/config",               // Should be ignored
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(testDir, file)
		if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
			t.Fatalf("failed to create directory: %v", err)
		}
		if err := os.WriteFile(fullPath, []byte("test content"), 0644); err != nil {
			t.Fatalf("failed to create test file %s: %v", file, err)
		}
	}

	tests := []struct {
		name           string
		pattern        string
		expectMatches  int
		expectContains []string
		expectExcludes []string
	}{
		{
			name:           "find all go files",
			pattern:        "**/*.go",
			expectMatches:  2,
			expectContains: []string{"test1.go", "subdir/test3.go"},
			expectExcludes: []string{"node_modules", ".git"},
		},
		{
			name:           "find all txt files",
			pattern:        "**/*.txt",
			expectMatches:  2,
			expectContains: []string{"test2.txt", "subdir/test4.txt"},
		},
		{
			name:           "find files in subdir",
			pattern:        "subdir/*",
			expectMatches:  2,
			expectContains: []string{"subdir/test3.go", "subdir/test4.txt"},
		},
		{
			name:          "no matches for non-existent pattern",
			pattern:       "**/*.xyz",
			expectMatches: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := fmt.Sprintf(`{"pattern": "%s"}`, tt.pattern)
			call := tools.ToolCall{Input: input}

			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
				WithWorkingDir(testDir).
				Build()
			response, err := tool.Run(toolCtx, call)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			// Check metadata
			var metadata specs.GlobResponseMetadata
			if response.Metadata != "" {
				if err := json.Unmarshal([]byte(response.Metadata), &metadata); err != nil {
					t.Fatalf("failed to parse metadata: %v", err)
				}
			}

			if metadata.NumberOfFiles != tt.expectMatches {
				t.Errorf("expected %d matches, got %d", tt.expectMatches, metadata.NumberOfFiles)
			}

			// Check content includes expected files
			for _, expected := range tt.expectContains {
				if !strings.Contains(response.Content, expected) {
					t.Errorf("expected output to contain %s", expected)
				}
			}

			// Check content excludes ignored files
			for _, excluded := range tt.expectExcludes {
				if strings.Contains(response.Content, excluded) {
					t.Errorf("expected output to exclude %s", excluded)
				}
			}
		})
	}
}

func TestGlobTool_Run_WithParameters(t *testing.T) {
	testDir := initGlobTestConfig(t)
	tool := NewGlobTool()

	// Create test files with different cases
	testFiles := []string{
		"Test1.GO",
		"test2.go",
		"TEST3.GO",
		"subdir/Test4.go",
	}

	for _, file := range testFiles {
		fullPath := filepath.Join(testDir, file)
		if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
			t.Fatalf("failed to create directory: %v", err)
		}
		if err := os.WriteFile(fullPath, []byte("test content"), 0644); err != nil {
			t.Fatalf("failed to create test file %s: %v", file, err)
		}
	}

	tests := []struct {
		name          string
		params        specs.GlobParams
		expectMatches int
		description   string
	}{
		{
			name:          "case sensitive search",
			params:        specs.GlobParams{Pattern: "**/*.go"},
			expectMatches: 2, // Only test2.go and Test4.go (depending on filesystem)
			description:   "case sensitive should match fewer files",
		},
		{
			name:          "pattern matching all files",
			params:        specs.GlobParams{Pattern: "**/*"},
			expectMatches: 4, // 4 files (subdir may be excluded by default)
			description:   "should match all files",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			inputBytes, _ := json.Marshal(tt.params)
			call := tools.ToolCall{Input: string(inputBytes)}

			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
				WithWorkingDir(testDir).
				Build()
			response, err := tool.Run(toolCtx, call)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			var metadata specs.GlobResponseMetadata
			if response.Metadata != "" {
				if err := json.Unmarshal([]byte(response.Metadata), &metadata); err != nil {
					t.Fatalf("failed to parse metadata: %v", err)
				}
			}

			// Note: Due to filesystem case sensitivity differences,
			// we'll be more flexible with case-sensitive tests
			if tt.name == "case sensitive search" {
				// Just check that we get some matches but not necessarily exact count
				if metadata.NumberOfFiles == 0 {
					t.Errorf("%s: expected some matches, got 0", tt.description)
				}
			} else {
				if metadata.NumberOfFiles != tt.expectMatches {
					t.Errorf("%s: expected %d matches, got %d", tt.description, tt.expectMatches, metadata.NumberOfFiles)
				}
			}
		})
	}
}

func TestGlobTool_Run_InvalidPath(t *testing.T) {
	tool := NewGlobTool()

	input := `{"pattern": "*.go", "path": "/nonexistent/path"}`
	call := tools.ToolCall{Input: input}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Errorf("expected error response, got actual error: %v", err)
	}

	if response.Type != tools.ToolResponseTypeText {
		t.Errorf("expected text response, got: %s", response.Type)
	}

	if !strings.Contains(response.Content, "Cannot access search path") {
		t.Errorf("expected path access error, got: %s", response.Content)
	}
}

func TestGlobTool_Run_ContextCancellation(t *testing.T) {
	initGlobTestConfig(t)
	tool := NewGlobTool()

	// Create a context that's immediately cancelled
	baseCtx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately
	toolCtx := tools.NewToolExecutionContextBuilder(baseCtx).Build()

	input := `{"pattern": "**/*"}`
	call := tools.ToolCall{Input: input}

	// This should still work as the cancellation mainly affects ripgrep
	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Errorf("unexpected error with cancelled context: %v", err)
	}

	if response.Type != tools.ToolResponseTypeText {
		t.Errorf("expected text response, got: %s", response.Type)
	}
}

// Benchmark tests
func BenchmarkGlobTool_Run(b *testing.B) {
	testDir := initGlobTestConfig(b)
	tool := NewGlobTool()

	// Create many test files
	for i := 0; i < 100; i++ {
		file := filepath.Join(testDir, fmt.Sprintf("test_%d.go", i))
		if err := os.WriteFile(file, []byte("package main"), 0644); err != nil {
			b.Fatalf("failed to create test file: %v", err)
		}
	}

	input := `{"pattern": "**/*.go"}`
	call := tools.ToolCall{Input: input}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
		_, err := tool.Run(toolCtx, call)
		if err != nil {
			b.Fatalf("benchmark run failed: %v", err)
		}
	}
}

func BenchmarkGlobTool_ProcessSearchPath(b *testing.B) {
	tool := &globTool{}
	paths := []string{
		"",
		"~/Documents",
		"/tmp/test",
		"src/main",
		"//usr/local",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).Build()
		for _, path := range paths {
			_, _ = tool.processSearchPath(toolCtx, path)
		}
	}
}

// Helper function to initialize test configuration for glob tests
func initGlobTestConfig(t testing.TB) string {
	// Use a more unique directory name to avoid conflicts between tests
	testDir := filepath.Join(os.TempDir(), "glob_tool_test", fmt.Sprintf("%s_%d", t.Name(), time.Now().UnixNano()))
	if err := os.RemoveAll(testDir); err != nil {
		t.Fatalf("failed to clean test directory: %v", err)
	}
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("failed to create test directory: %v", err)
	}

	// 使用 t.Cleanup 确保在测试用例完全结束后才删除目录
	t.Cleanup(func() {
		os.RemoveAll(testDir)
	})

	return testDir
}
