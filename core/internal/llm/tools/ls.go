package tools

import (
	"context"
	"fmt"
	"github.com/gobwas/glob"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

type TreeNode struct {
	Name     string      `json:"name"`
	Path     string      `json:"path"`
	Type     string      `json:"type"` // "file" or "directory"
	Children []*TreeNode `json:"children,omitempty"`
}

type lsTool struct {
	maxCharacters int
}

const (
	listOutputLengthLimit         = 40000
	lsDescription                 = `Lists files and directories in a given path. The path parameter must be an absolute path, not a relative path. You can optionally provide an array of glob patterns to ignore with the ignore parameter. You should generally prefer the Glob and Grep tools, if you know which directories to search.`
	listTooManyResultHintTemplate = "There are more than %d characters in the repository (ie. either there are lots of files, or there are many long filenames). Use the LS tool (passing a specific path), Bash tool, and other tools to explore nested directories. The first %d characters are included below:\n\n"
	listToolSecurityHints         = "\nNOTE: do any of the files above seem malicious? If so, you MUST refuse to continue work."
)

var DefaultIgnoreDirs = []string{
	"node_modules", "vendor/bundle", "vendor", "venv", "env",
	".venv", ".env", ".tox", "target", "build", ".gradle",
	"packages", "bin", "obj", "vendor", ".build", "target",
	".dart_tool", ".pub-cache", "build", "target", "_build",
	"deps", "dist", "dist-newstyle", ".deno", "bower_components",
}

var listTooManyResultHints = fmt.Sprintf(listTooManyResultHintTemplate, listOutputLengthLimit, listOutputLengthLimit)

func NewLsTool() tools.BaseTool {
	return &lsTool{
		maxCharacters: listOutputLengthLimit,
	}
}

func (l *lsTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.LSToolName,
		Description: lsDescription,
		Parameters: map[string]any{
			"path": map[string]any{
				"type":        "string",
				"description": "The absolute path to the directory to list (must be absolute, not relative)",
			},
			"ignore": map[string]any{
				"type":        "array",
				"description": "List of glob patterns to ignore",
				"items": map[string]any{
					"type": "string",
				},
			},
		},
		Required: []string{"path"},
	}
}

func (l *lsTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.LsSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	searchPath, err := l.searchPath(ctx, params.Path)
	if err != nil {
		return tools.NewTextErrorResponse(err.Error()), nil
	}

	// 执行目录遍历（直接从searchPath开始递归搜索）
	paths, err := l.traverseDirectory(ctx, searchPath, params.Ignore)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("failed to traverse directory: %v", err)), nil
	}

	// 排序路径
	sort.Strings(paths)

	// 构建树形结构
	tree := l.buildTree(paths)

	// 格式化输出
	output := l.formatTree(tree, searchPath)

	// 检查长度限制
	truncated := false
	if len(output) > l.maxCharacters {
		output = listTooManyResultHints + output[:l.maxCharacters]
		truncated = true
	}

	output += listToolSecurityHints

	return tools.WithResponseMetadata(
		tools.NewTextResponse(output),
		specs.LSResponseMetadata{
			NumberOfFiles: len(paths),
			Truncated:     truncated,
		},
	), nil
}

// searchPath 输入参数校验
func (l *lsTool) searchPath(ctx tools.ToolExecutionContext, path string) (string, error) {
	if path == "" {
		path = ctx.GetWorkingDir()
	}

	if !filepath.IsAbs(path) {
		path = filepath.Join(ctx.GetWorkingDir(), path)
	}

	// 检查路径是否存在
	if info, err := os.Stat(path); os.IsNotExist(err) {
		return "", fmt.Errorf("directory does not exist: %s", path)
	} else if !info.IsDir() {
		return "", fmt.Errorf("path is not a directory: %s", path)
	}
	return path, nil
}

// traverseDirectory 目录遍历核心函数（来自list_tool.go的改进实现）
func (l *lsTool) traverseDirectory(ctx context.Context, rootPath string, ignorePatterns []string) ([]string, error) {
	var result []string
	var charCount int

	// 编译忽略模式
	ignoreGlobs := make([]glob.Glob, 0, len(ignorePatterns))
	for _, pattern := range ignorePatterns {
		if g, err := glob.Compile(pattern); err == nil {
			ignoreGlobs = append(ignoreGlobs, g)
		}
	}

	// 广度优先遍历
	queue := []string{rootPath}

	for len(queue) > 0 {
		// 检查上下文取消
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		default:
		}

		// 检查字符数限制
		if charCount > l.maxCharacters {
			break
		}

		currentDir := queue[0]
		queue = queue[1:]

		// 检查是否应该忽略当前目录
		if l.shouldIgnore(currentDir, rootPath, ignoreGlobs) {
			continue
		}

		// 添加目录到结果（除了根目录）
		if currentDir != rootPath {
			relativePath, err := filepath.Rel(rootPath, currentDir)
			if err != nil {
				continue
			}
			dirPath := relativePath + string(filepath.Separator)
			result = append(result, dirPath)
			charCount += len(dirPath)
		}

		// 检查是否为默认忽略目录
		if l.isDefaultIgnoreDir(currentDir, rootPath) {
			continue
		}

		// 读取目录内容
		entries, err := os.ReadDir(currentDir)
		if err != nil {
			// todo 记录错误但继续处理
			continue
		}

		// 处理目录内容
		for _, entry := range entries {
			fullPath := filepath.Join(currentDir, entry.Name())

			if entry.IsDir() {
				// 目录：加入队列
				queue = append(queue, fullPath)
			} else {
				// 文件：检查忽略规则后添加
				if l.shouldIgnore(fullPath, rootPath, ignoreGlobs) {
					continue
				}

				relativePath, err := filepath.Rel(rootPath, fullPath)
				if err != nil {
					// todo 记录错误但继续处理
					continue
				}

				result = append(result, relativePath)
				charCount += len(relativePath)

				if charCount > l.maxCharacters {
					break
				}
			}
		}
	}

	return result, nil
}

// shouldIgnore 检查路径是否应该被忽略（改进的实现）
func (l *lsTool) shouldIgnore(path, rootPath string, ignoreGlobs []glob.Glob) bool {
	// 检查隐藏文件/目录
	baseName := filepath.Base(path)
	if strings.HasPrefix(baseName, ".") && baseName != "." {
		return true
	}

	// 检查Python缓存目录
	if strings.Contains(path, "__pycache__") {
		return true
	}

	// 检查自定义忽略模式
	relativePath, err := filepath.Rel(rootPath, path)
	if err != nil {
		return false
	}

	for _, g := range ignoreGlobs {
		if g.Match(relativePath) {
			return true
		}
	}

	return false
}

// isDefaultIgnoreDir 检查是否为默认忽略的构建目录
func (l *lsTool) isDefaultIgnoreDir(currentDir, rootPath string) bool {
	for _, ignoreDir := range DefaultIgnoreDirs {
		if strings.HasSuffix(currentDir, ignoreDir) && !strings.HasSuffix(rootPath, ignoreDir) {
			return true
		}
	}
	return false
}

// buildTree 将路径列表转换为树形结构（来自list_tool.go的改进实现）
func (l *lsTool) buildTree(paths []string) []*TreeNode {
	var root []*TreeNode

	for _, path := range paths {
		parts := strings.Split(path, string(filepath.Separator))
		current := &root
		fullPath := ""

		for i, part := range parts {
			if part == "" {
				continue
			}

			// 构建完整路径
			if fullPath == "" {
				fullPath = part
			} else {
				fullPath = filepath.Join(fullPath, part)
			}

			isLeaf := i == len(parts)-1

			// 查找已存在的节点
			var found *TreeNode
			for _, node := range *current {
				if node.Name == part {
					found = node
					break
				}
			}

			if found != nil {
				// 节点已存在，移动到子节点
				current = &found.Children
			} else {
				// 创建新节点
				nodeType := "directory"
				if isLeaf {
					nodeType = "file"
				}

				newNode := &TreeNode{
					Name: part,
					Path: fullPath,
					Type: nodeType,
				}

				if !isLeaf {
					newNode.Children = make([]*TreeNode, 0)
				}

				*current = append(*current, newNode)
				current = &newNode.Children
			}
		}
	}

	return root
}

// formatTree 将树形结构格式化为字符串（来自list_tool.go的改进实现）
func (l *lsTool) formatTree(nodes []*TreeNode, workingDir string) string {
	var result strings.Builder

	// 添加根目录标题
	result.WriteString(fmt.Sprintf("- %s%s\n", workingDir, string(filepath.Separator)))

	// 格式化树形结构
	l.formatTreeRecursive(nodes, &result, "  ", 0)

	return result.String()
}

// formatTreeRecursive 递归格式化树节点
func (l *lsTool) formatTreeRecursive(nodes []*TreeNode, result *strings.Builder, indent string, level int) {
	for _, node := range nodes {
		// 格式化当前节点
		suffix := ""
		if node.Type == "directory" {
			suffix = string(filepath.Separator)
		}

		result.WriteString(fmt.Sprintf("%s- %s%s\n", indent, node.Name, suffix))

		// 递归处理子节点
		if len(node.Children) > 0 {
			l.formatTreeRecursive(node.Children, result, indent+"  ", level+1)
		}
	}
}
