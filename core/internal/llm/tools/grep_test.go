package tools

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/utils/fileutil"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func initGrepTestConfig(t *testing.T) string {
	wd := t.TempDir()
	return wd
}

func checkRipgrepAvailable(t *testing.T) {
	rgPath := fileutil.GetRgPath()
	if rgPath == "" {
		t.Skip("ripgrep (rg) not found in PATH, skipping test")
	}
}

func TestGrepTool_Info(t *testing.T) {
	tool := NewGrepTool()
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.GrepToolName {
		t.<PERSON>rrorf("Expected name %s, got %s", tools.GrepToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["pattern"]; !ok {
		t.Error("pattern parameter should be defined")
	}

	if _, ok := info.Parameters["output_mode"]; !ok {
		t.Error("output_mode parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) != 1 || info.Required[0] != "pattern" {
		t.Errorf("Expected required parameter 'pattern', got %v", info.Required)
	}
}

func TestGrepTool_Run_BasicSearch(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.go")
	content := `package main

import "fmt"

func main() {
	fmt.Println("Hello, World!")
	fmt.Println("This is a test")
}
`
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.GrepParams{
		Pattern:    "fmt",
		Path:       tempDir,
		OutputMode: "files_with_matches",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证响应包含文件路径
	if !strings.Contains(response.Content, testFile) {
		t.Errorf("Response should contain test file path: %s", response.Content)
	}
}

func TestGrepTool_Run_ContentMode(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	content := `Line 1: hello world
Line 2: test content
Line 3: hello again
Line 4: end of file
`
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	lineNumbers := true
	params := specs.GrepParams{
		Pattern:     "hello",
		Path:        tempDir,
		OutputMode:  "content",
		LineNumbers: &lineNumbers,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证响应包含匹配内容
	if !strings.Contains(response.Content, "hello world") {
		t.Errorf("Response should contain matching content: %s", response.Content)
	}

	if !strings.Contains(response.Content, "hello again") {
		t.Errorf("Response should contain second match: %s", response.Content)
	}
}

func TestGrepTool_Run_CountMode(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	content := `test line 1
test line 2
other line
test line 3
`
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.GrepParams{
		Pattern:    "test",
		Path:       tempDir,
		OutputMode: "count",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证响应包含计数信息
	if !strings.Contains(response.Content, "3 total") {
		t.Errorf("Response should contain count of 3 matches: %s", response.Content)
	}
}

func TestGrepTool_Run_WithContext(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	content := `line 1
line 2
MATCH HERE
line 4
line 5
`
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	contextLines := 1
	params := specs.GrepParams{
		Pattern:    "MATCH",
		Path:       tempDir,
		OutputMode: "content",
		ContextC:   &contextLines,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证响应包含上下文行
	if !strings.Contains(response.Content, "line 2") {
		t.Errorf("Response should contain context before match: %s", response.Content)
	}

	if !strings.Contains(response.Content, "line 4") {
		t.Errorf("Response should contain context after match: %s", response.Content)
	}
}

func TestGrepTool_Run_WithGlob(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	// Go文件
	goFile := filepath.Join(tempDir, "test.go")
	goContent := `package main
func test() {
	// test function
}
`
	if err := os.WriteFile(goFile, []byte(goContent), 0644); err != nil {
		t.Fatalf("Failed to create go file: %v", err)
	}

	// JS文件
	jsFile := filepath.Join(tempDir, "test.js")
	jsContent := `function test() {
	console.log("test");
}
`
	if err := os.WriteFile(jsFile, []byte(jsContent), 0644); err != nil {
		t.Fatalf("Failed to create js file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.GrepParams{
		Pattern:    "test",
		Path:       tempDir,
		Glob:       "*.go",
		OutputMode: "files_with_matches",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证只包含Go文件
	if !strings.Contains(response.Content, "test.go") {
		t.Errorf("Response should contain go file: %s", response.Content)
	}

	if strings.Contains(response.Content, "test.js") {
		t.Errorf("Response should not contain js file when filtering by *.go: %s", response.Content)
	}
}

func TestGrepTool_Run_IgnoreCase(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	content := `Hello World
hello world
HELLO WORLD
`
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	ignoreCase := true
	params := specs.GrepParams{
		Pattern:    "hello",
		Path:       tempDir,
		OutputMode: "count",
		IgnoreCase: &ignoreCase,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证匹配了所有3行（忽略大小写）
	if !strings.Contains(response.Content, "3 total") {
		t.Errorf("Response should contain count of 3 matches with ignore case: %s", response.Content)
	}
}

func TestGrepTool_Run_NoMatches(t *testing.T) {
	checkRipgrepAvailable(t)
	tempDir := initGrepTestConfig(t)

	tool := NewGrepTool()

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	content := `line 1
line 2
line 3
`
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.GrepParams{
		Pattern:    "nonexistent",
		Path:       tempDir,
		OutputMode: "files_with_matches",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证没有匹配结果
	if !strings.Contains(response.Content, "No files found") {
		t.Errorf("Response should indicate no matches found: %s", response.Content)
	}
}

func TestGrepTool_Run_InvalidJSON(t *testing.T) {
	tool := NewGrepTool()

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: "invalid json",
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for invalid JSON")
	}

	if !strings.Contains(response.Content, "error parsing parameters") {
		t.Errorf("Expected JSON parsing error, got: %s", response.Content)
	}
}

func TestGrepTool_Run_MissingPattern(t *testing.T) {
	tool := NewGrepTool()

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.GrepParams{
		Pattern: "", // 空模式
		Path:    "/tmp",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.GrepToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for missing pattern")
	}

	if response.Content != "pattern is required" {
		t.Errorf("Expected pattern required error, got: %s", response.Content)
	}
}

func TestGrepTool_ParseGlobPatterns(t *testing.T) {
	tool := &grepTool{}

	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "Simple glob",
			input:    "*.go",
			expected: []string{"*.go"},
		},
		{
			name:     "Multiple globs with comma",
			input:    "*.go,*.js",
			expected: []string{"*.go", "*.js"},
		},
		{
			name:     "Multiple globs with space",
			input:    "*.go *.js",
			expected: []string{"*.go", "*.js"},
		},
		{
			name:     "Brace expansion",
			input:    "*.{go,js}",
			expected: []string{"*.{go,js}"},
		},
		{
			name:     "Complex pattern",
			input:    "src/**/*.{go,js} *.md",
			expected: []string{"src/**/*.{go,js}", "*.md"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.parseGlobPatterns(tt.input)
			if len(result) != len(tt.expected) {
				t.Errorf("Expected %d patterns, got %d", len(tt.expected), len(result))
			}
			for i, expected := range tt.expected {
				if i >= len(result) || result[i] != expected {
					t.Errorf("Expected pattern %s, got %s", expected, result[i])
				}
			}
		})
	}
}

func TestGrepTool_ResolveSearchPath(t *testing.T) {
	tempDir := initGrepTestConfig(t)
	tool := &grepTool{}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Empty path",
			input:    "",
			expected: tempDir,
		},
		{
			name:     "Absolute path",
			input:    "/usr/local",
			expected: "/usr/local",
		},
		{
			name:     "Relative path",
			input:    "src",
			expected: filepath.Join(tempDir, "src"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
				WithWorkingDir(tempDir).
				Build()
			result := tool.resolveSearchPath(toolCtx, tt.input)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestGrepTool_Pluralize(t *testing.T) {
	tool := &grepTool{}

	tests := []struct {
		name     string
		word     string
		count    int
		expected string
	}{
		{
			name:     "file 1",
			word:     "file",
			count:    1,
			expected: "file",
		},
		{
			name:     "file 2",
			word:     "file",
			count:    2,
			expected: "files",
		},
		{
			name:     "occurrence 1",
			word:     "occurrence",
			count:    1,
			expected: "occurrence",
		},
		{
			name:     "occurrence 5",
			word:     "occurrence",
			count:    5,
			expected: "occurrences",
		},
		{
			name:     "match 0",
			word:     "match",
			count:    0,
			expected: "matchs",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.pluralize(tt.word, tt.count)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}
