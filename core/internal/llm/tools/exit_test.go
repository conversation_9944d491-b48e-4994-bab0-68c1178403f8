package tools

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"testing"
)

// mockPermissionService 模拟权限服务用于测试
type mockPermissionService struct {
	shouldApprove bool
	lastRequest   *core.CreatePermissionRequest
}

func (m *mockPermissionService) GrantPersistent(permission core.PermissionRequest) {}
func (m *mockPermissionService) Grant(permission core.PermissionRequest)           {}
func (m *mockPermissionService) Deny(permission core.PermissionRequest)            {}
func (m *mockPermissionService) AutoApproveSession(sessionID string)               {}

// 实现pubsub.Suscriber接口
func (m *mockPermissionService) Subscribe(ctx context.Context) <-chan pubsub.Event[core.PermissionRequest] {
	ch := make(chan pubsub.Event[core.PermissionRequest])
	// 对于测试，我们不需要实际的事件，直接返回一个会被关闭的channel
	close(ch)
	return ch
}

func (m *mockPermissionService) CreateRequest(opts core.CreatePermissionRequest) bool {
	m.lastRequest = &opts
	return m.shouldApprove
}

func TestExitPlanModeTool_Info(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.ExitPlanModeToolName {
		t.Errorf("Expected name %s, got %s", tools.ExitPlanModeToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["plan"]; !ok {
		t.Error("plan parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) != 1 || info.Required[0] != "plan" {
		t.Error("plan should be the only required parameter")
	}

	// 验证plan参数的类型
	planParam := info.Parameters["plan"].(map[string]any)
	if planParam["type"].(string) != "string" {
		t.Error("plan parameter should be of type string")
	}
}

func TestExitPlanModeTool_Run_Success(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)

	// 创建带有sessionID和messageID的上下文
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.ExitPlanModeParams{
		Plan: "我的实施计划：\n1. 创建用户界面\n2. 实现后端API\n3. 编写测试",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	expectedContent := "User has approved your plan. You can now start coding. Start with updating your todo list if applicable"
	if response.Content != expectedContent {
		t.Errorf("Expected content %q, got %q", expectedContent, response.Content)
	}

	// 验证权限请求
	if mockPermissions.lastRequest == nil {
		t.Error("Expected permission request to be made")
	} else {
		req := mockPermissions.lastRequest
		if req.SessionId != "test-session-123" {
			t.Errorf("Expected sessionID %q, got %q", "test-session-123", req.SessionId)
		}
		if req.ToolName != tools.ExitPlanModeToolName {
			t.Errorf("Expected toolName %q, got %q", tools.ExitPlanModeToolName, req.ToolName)
		}
		if req.Action != "exit_plan_mode" {
			t.Errorf("Expected action %q, got %q", "exit_plan_mode", req.Action)
		}
		if req.Description != "Exit plan mode?" {
			t.Errorf("Expected description %q, got %q", "Exit plan mode?", req.Description)
		}

		// 验证权限参数
		permParams, ok := req.Params.(specs.ExitPlanModePermissionParams)
		if !ok {
			t.Error("Expected params to be ExitPlanModePermissionParams")
		} else if permParams.Plan != params.Plan {
			t.Errorf("Expected plan %q, got %q", params.Plan, permParams.Plan)
		}
	}
}

func TestExitPlanModeTool_Run_PermissionDenied(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: false}
	tool := NewExitPlanModeTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.ExitPlanModeParams{
		Plan: "测试计划",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: string(input),
	}

	_, err = tool.Run(toolCtx, call)

	// 应该返回权限拒绝错误
	if err == nil {
		t.Error("Expected permission denied error")
	}

	if err != core.ErrorPermissionDenied {
		t.Errorf("Expected permission denied error, got %v", err)
	}
}

func TestExitPlanModeTool_Run_EmptyPlan(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	params := specs.ExitPlanModeParams{
		Plan: "",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for empty plan")
	}

	if response.Content != "plan is required" {
		t.Errorf("Expected error message 'plan is required', got %q", response.Content)
	}
}

func TestExitPlanModeTool_Run_InvalidJSON(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: "invalid json",
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 应该返回错误响应
	if !response.IsError {
		t.Error("Expected error response for invalid JSON")
	}

	if response.Content != "invalid parameters" {
		t.Errorf("Expected error message 'invalid parameters', got %q", response.Content)
	}
}

func TestExitPlanModeTool_Run_MissingSessionID(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)

	// Create toolCtx with missing sessionID to test error
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithMessageID("test-message-456").
		Build()

	params := specs.ExitPlanModeParams{
		Plan: "测试计划",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: string(input),
	}

	_, err = tool.Run(toolCtx, call)

	// 应该返回错误
	if err == nil {
		t.Error("Expected error for missing sessionID")
	}

	expectedError := "session Id and message Id are required"
	if err.Error() != expectedError {
		t.Errorf("Expected error message %q, got %q", expectedError, err.Error())
	}
}

func TestExitPlanModeTool_Run_MissingMessageID(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)

	// Create toolCtx with missing messageID to test error
	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		Build()

	params := specs.ExitPlanModeParams{
		Plan: "测试计划",
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: string(input),
	}

	_, err = tool.Run(toolCtx, call)

	// 应该返回错误
	if err == nil {
		t.Error("Expected error for missing messageID")
	}

	expectedError := "session Id and message Id are required"
	if err.Error() != expectedError {
		t.Errorf("Expected error message %q, got %q", expectedError, err.Error())
	}
}

func TestExitPlanModeTool_Run_ComplexPlan(t *testing.T) {
	mockPermissions := &mockPermissionService{shouldApprove: true}
	tool := NewExitPlanModeTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	// 测试包含markdown格式的复杂计划
	complexPlan := `# 实施计划

## 第一阶段：基础设施
- [ ] 设置数据库连接
- [ ] 配置路由系统
- [ ] 创建基础中间件

## 第二阶段：功能开发
1. **用户管理**
   - 注册功能
   - 登录验证
   - 权限控制

2. **核心业务逻辑**
   - API设计
   - 数据处理
   - 错误处理

## 第三阶段：测试和优化
- 单元测试
- 集成测试
- 性能优化

> 注意：每个阶段完成后需要进行代码审查
`

	params := specs.ExitPlanModeParams{
		Plan: complexPlan,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.ExitPlanModeToolName,
		Input: string(input),
	}

	response, err := tool.Run(toolCtx, call)
	if err != nil {
		t.Fatalf("Failed to run tool: %v", err)
	}

	// 验证响应
	if response.IsError {
		t.Errorf("Expected successful response, got error: %s", response.Content)
	}

	// 验证权限请求中包含完整的计划
	if mockPermissions.lastRequest != nil {
		permParams, ok := mockPermissions.lastRequest.Params.(specs.ExitPlanModePermissionParams)
		if !ok {
			t.Error("Expected params to be ExitPlanModePermissionParams")
		} else if permParams.Plan != complexPlan {
			t.Error("Complex plan should be preserved in permission request")
		}
	}
}
