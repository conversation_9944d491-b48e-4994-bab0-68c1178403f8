package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/anthropics/anthropic-sdk-go"
	"github.com/anthropics/anthropic-sdk-go/packages/param"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
)

type webSearchTool struct{}

const (
	webSearchDescription = `- <PERSON>ows <PERSON> to search the web and use the results to inform responses
- Provides up-to-date information for current events and recent data
- Returns search result information formatted as search result blocks
- Use this tool for accessing information beyond <PERSON>'s knowledge cutoff
- Searches are performed automatically within a single API call

Usage notes:
  - Domain filtering is supported to include or block specific websites
  - Web search is only available in the US`
)

func NewWebSearchTool() tools.BaseTool {
	return &webSearchTool{}
}

func (t webSearchTool) Info() tools.ToolInfo {
	return tools.ToolInfo{
		Name:        tools.WebSearchToolName,
		Description: webSearchDescription,
		Parameters: map[string]any{
			"query": map[string]any{
				"type":        "string",
				"description": "The absolute path to the file to read",
			},
			"allowed_domains": map[string]any{
				"type": "array",
				"items": map[string]any{
					"type": "string",
				},
				"description": "Only include search results from these domains",
			},
			"blocked_domains": map[string]any{
				"type": "array",
				"items": map[string]any{
					"type": "string",
				},
				"description": "Never include search results from these domains",
			},
		},
		Required: []string{"query"},
	}
}

func convertToLinkList(blocks []map[string]string) (string, error) {
	if len(blocks) > 0 {
		list, err := json.Marshal(blocks)
		if err != nil {
			return "", err
		}
		return fmt.Sprintf("Links: %s", string(list)), nil
	}
	return "No links found.", nil
}

func (t webSearchTool) Run(ctx tools.ToolExecutionContext, call tools.ToolCall) (tools.ToolResponse, error) {
	params, err := specs.WebSearchSpec.GetParams(call.Input)
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("error parsing parameters: %s", err)), nil
	}

	if params.Query == "" {
		return tools.NewTextErrorResponse("Error: Missing query"), nil
	}

	if len(params.BlockedDomains) > 0 && len(params.AllowedDomains) > 0 {
		return tools.NewTextErrorResponse("Error: Cannot specify both allowed_domains and blocked_domains in the same request"), nil
	}

	messages := []anthropic.MessageParam{
		anthropic.NewUserMessage(anthropic.NewTextBlock("You are an assistant for performing a web search tool use")),
		anthropic.NewUserMessage(anthropic.NewTextBlock("Perform a web search for the query: " + params.Query)),
	}

	client := anthropic.NewClient()
	message, err := client.Messages.New(context.TODO(), anthropic.MessageNewParams{
		Model:     anthropic.ModelClaudeSonnet4_20250514,
		MaxTokens: 1024,
		Messages:  messages,
		Tools: []anthropic.ToolUnionParam{{
			OfWebSearchTool20250305: &anthropic.WebSearchTool20250305Param{
				Type:           "web_search_tool_20250305",
				Name:           "web_search",
				MaxUses:        param.Opt[int64]{Value: 5},
				AllowedDomains: params.AllowedDomains,
				BlockedDomains: params.BlockedDomains,
			}},
		},
	})
	if err != nil {
		return tools.NewTextErrorResponse(fmt.Sprintf("Error: %s", err)), nil
	}

	result := fmt.Sprintf(`Web search results for query: "%s"\n\n`, params.Query)
	var blocks []map[string]string
	for _, block := range message.Content {
		switch block := block.AsAny().(type) {
		case anthropic.TextBlock:
			if len(blocks) > 0 {
				list, err := convertToLinkList(blocks)
				if err != nil {
					return tools.NewTextErrorResponse(fmt.Sprintf("Error: %s", err)), nil
				}
				result += list + "\n"
				blocks = []map[string]string{}
			}
			result += block.Text + "\n"
		case anthropic.WebSearchToolResultBlock:
			for _, item := range block.Content.OfWebSearchResultBlockArray {
				blocks = append(blocks, map[string]string{
					"title": item.Title,
					"url":   item.URL,
				})
			}
		}
	}
	if len(blocks) > 0 {
		list, err := convertToLinkList(blocks)
		if err != nil {
			return tools.NewTextErrorResponse(fmt.Sprintf("Error: %s", err)), nil
		}
		result += list + "\n"
	}
	return tools.NewTextResponse(result), nil
}
