package tools

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// Mock permission service for testing
type mockBashPermissionService struct {
	shouldApprove bool
}

func (m *mockBashPermissionService) CreateRequest(req core.CreatePermissionRequest) bool {
	return m.shouldApprove
}

func (m *mockBashPermissionService) Grant(req core.PermissionRequest)           {}
func (m *mockBashPermissionService) Deny(req core.PermissionRequest)            {}
func (m *mockBashPermissionService) GrantPersistent(req core.PermissionRequest) {}
func (m *mockBashPermissionService) AutoApproveSession(sessionID string)        {}
func (m *mockBashPermissionService) Subscribe(ctx context.Context) <-chan pubsub.Event[core.PermissionRequest] {
	ch := make(chan pubsub.Event[core.PermissionRequest])
	go func() {
		<-ctx.Done()
		close(ch)
	}()
	return ch
}

// setupTestDir 创建临时测试目录
func setupTestDir(t *testing.T) (string, func()) {
	t.Helper()

	tempDir, err := os.MkdirTemp("", "bash_tool_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// 创建一些测试文件
	testFiles := map[string]string{
		"test.txt":  "Hello World\nThis is a test file\n",
		"empty.txt": "",
		"data.json": `{"name": "test", "value": 123}`,
		"script.sh": "#!/bin/bash\necho 'Hello from script'\n",
	}

	for filename, content := range testFiles {
		filepath := filepath.Join(tempDir, filename)
		if err := os.WriteFile(filepath, []byte(content), 0644); err != nil {
			t.Fatalf("Failed to create test file %s: %v", filename, err)
		}
	}

	// 确保script.sh可执行
	if err := os.Chmod(filepath.Join(tempDir, "script.sh"), 0755); err != nil {
		t.Fatalf("Failed to make script.sh executable: %v", err)
	}

	// 返回清理函数
	cleanup := func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Logf("Failed to cleanup temp dir: %v", err)
		}
	}

	return tempDir, cleanup
}

func TestBashTool_Info(t *testing.T) {
	mockPermissions := &mockBashPermissionService{shouldApprove: true}
	tool := NewBashTool(mockPermissions)
	info := tool.Info()

	// 验证基本信息
	if info.Name != tools.BashToolName {
		t.Errorf("Expected name %s, got %s", tools.BashToolName, info.Name)
	}

	if info.Description == "" {
		t.Error("Description should not be empty")
	}

	// 验证参数定义
	if _, ok := info.Parameters["command"]; !ok {
		t.Error("command parameter should be defined")
	}

	if _, ok := info.Parameters["timeout"]; !ok {
		t.Error("timeout parameter should be defined")
	}

	// 验证必需参数
	if len(info.Required) != 1 || info.Required[0] != "command" {
		t.Errorf("Expected required parameter 'command', got %v", info.Required)
	}
}

func TestBashTool_InterpretExitCode(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name     string
		command  string
		exitCode int
		stdout   string
		stderr   string
		expected ExitCodeResult
	}{
		{
			name:     "grep with no matches",
			command:  "grep pattern file.txt",
			exitCode: 1,
			stdout:   "",
			stderr:   "",
			expected: ExitCodeResult{IsError: false, Message: "No matches found"},
		},
		{
			name:     "grep with error",
			command:  "grep pattern",
			exitCode: 2,
			stdout:   "",
			stderr:   "grep: missing file",
			expected: ExitCodeResult{IsError: true, Message: "grep failed with exit code 2"},
		},
		{
			name:     "ripgrep with no matches",
			command:  "rg pattern",
			exitCode: 1,
			stdout:   "",
			stderr:   "",
			expected: ExitCodeResult{IsError: false, Message: "No matches found"},
		},
		{
			name:     "find with inaccessible directories",
			command:  "find /root -name '*.txt'",
			exitCode: 1,
			stdout:   "/home/<USER>",
			stderr:   "find: '/root': Permission denied",
			expected: ExitCodeResult{IsError: false, Message: "Some directories were inaccessible"},
		},
		{
			name:     "diff showing differences",
			command:  "diff file1.txt file2.txt",
			exitCode: 1,
			stdout:   "< line1\n> line2",
			stderr:   "",
			expected: ExitCodeResult{IsError: false, Message: "Files differ"},
		},
		{
			name:     "test condition false",
			command:  "test -f nonexistent.txt",
			exitCode: 1,
			stdout:   "",
			stderr:   "",
			expected: ExitCodeResult{IsError: false, Message: "Condition is false"},
		},
		{
			name:     "default command success",
			command:  "echo hello",
			exitCode: 0,
			stdout:   "hello",
			stderr:   "",
			expected: ExitCodeResult{IsError: false, Message: ""},
		},
		{
			name:     "default command failure",
			command:  "invalidcommand",
			exitCode: 127,
			stdout:   "",
			stderr:   "command not found",
			expected: ExitCodeResult{IsError: true, Message: "Command failed with exit code 127"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.interpretExitCode(tt.command, tt.exitCode, tt.stdout, tt.stderr)
			if result.IsError != tt.expected.IsError {
				t.Errorf("Expected IsError %v, got %v", tt.expected.IsError, result.IsError)
			}
			if result.Message != tt.expected.Message {
				t.Errorf("Expected message '%s', got '%s'", tt.expected.Message, result.Message)
			}
		})
	}
}

func TestBashTool_TrackGitOperations(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name     string
		command  string
		exitCode int
		should   string
	}{
		{
			name:     "git commit success",
			command:  "git commit -m 'test commit'",
			exitCode: 0,
			should:   "track commit operation",
		},
		{
			name:     "git commit failure",
			command:  "git commit -m 'test commit'",
			exitCode: 1,
			should:   "not track failed operation",
		},
		{
			name:     "gh pr create success",
			command:  "gh pr create --title 'test pr'",
			exitCode: 0,
			should:   "track pr creation",
		},
		{
			name:     "regular command",
			command:  "echo hello",
			exitCode: 0,
			should:   "not track non-git operations",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里只是确保函数不会panic
			// 实际的指标记录需要mock系统
			tool.trackGitOperations(tt.command, tt.exitCode)
		})
	}
}

func TestBashTool_TrimEmptyLines(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "no empty lines",
			input:    "line1\nline2\nline3",
			expected: "line1\nline2\nline3",
		},
		{
			name:     "leading empty lines",
			input:    "\n\nline1\nline2",
			expected: "line1\nline2",
		},
		{
			name:     "trailing empty lines",
			input:    "line1\nline2\n\n\n",
			expected: "line1\nline2",
		},
		{
			name:     "both leading and trailing",
			input:    "\n\nline1\nline2\n\n",
			expected: "line1\nline2",
		},
		{
			name:     "only empty lines",
			input:    "\n\n\n",
			expected: "",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "single line with spaces",
			input:    "   line1   ",
			expected: "   line1   ",
		},
		{
			name:     "empty lines with spaces",
			input:    "  \n\nline1\n  \n",
			expected: "line1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.trimEmptyLines(tt.input)
			if result != tt.expected {
				t.Errorf("Expected '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestBashTool_IsImageContent(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name     string
		content  string
		expected bool
	}{
		{
			name:     "valid image data",
			content:  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
			expected: true,
		},
		{
			name:     "valid jpeg data",
			content:  "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQAB...",
			expected: true,
		},
		{
			name:     "valid gif data",
			content:  "data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAA...",
			expected: true,
		},
		{
			name:     "regular text",
			content:  "This is just regular text",
			expected: false,
		},
		{
			name:     "base64 but not image",
			content:  "data:text/plain;base64,VGhpcyBpcyBhIHRlc3Q=",
			expected: false,
		},
		{
			name:     "empty string",
			content:  "",
			expected: false,
		},
		{
			name:     "partial image data pattern",
			content:  "image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.isImageContent(tt.content)
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestBashTool_TruncateContent(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "short content",
			content:  "short text",
			expected: "short text",
		},
		{
			name:     "content at limit",
			content:  strings.Repeat("a", MaxOutputLength),
			expected: strings.Repeat("a", MaxOutputLength),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.truncateContent(tt.content)
			if len(tt.content) <= MaxOutputLength {
				// 短内容应该保持不变
				if result != tt.expected {
					t.Errorf("Expected '%s', got '%s'", tt.expected, result)
				}
			} else {
				// 长内容应该被截断
				if len(result) >= len(tt.content) {
					t.Error("Content should be truncated")
				}
				if !strings.Contains(result, "lines truncated") {
					t.Error("Truncated content should contain truncation message")
				}
			}
		})
	}
}

func TestBashTool_TruncateContent_LongContent(t *testing.T) {
	tool := &bashTool{}

	// 创建超过限制的长内容
	longContent := strings.Repeat("This is a test line.\n", 2000) // 约40000字符
	result := tool.truncateContent(longContent)

	// 验证截断结果
	if len(result) >= len(longContent) {
		t.Error("Long content should be truncated")
	}

	if !strings.Contains(result, "lines truncated") {
		t.Error("Truncated content should contain truncation message")
	}

	// 验证截断消息的格式
	if !strings.Contains(result, "... [") || !strings.Contains(result, " lines truncated] ...") {
		t.Error("Truncation message format is incorrect")
	}
}

func TestBashTool_ProcessContent(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "empty content",
			content:  "",
			expected: "",
		},
		{
			name:     "content with leading/trailing empty lines",
			content:  "\n\nhello world\n\n",
			expected: "hello world",
		},
		{
			name:     "image content",
			content:  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
			expected: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
		},
		{
			name:     "regular short content",
			content:  "hello\nworld",
			expected: "hello\nworld",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, result := tool.processContent(tt.content)
			if result != tt.expected {
				t.Errorf("Expected '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestBashTool_BuildResponse(t *testing.T) {
	tool := &bashTool{}

	tests := []struct {
		name           string
		stdout         string
		stderr         string
		exitCode       int
		interrupted    bool
		exitCodeResult ExitCodeResult
		expectedParts  []string
	}{
		{
			name:           "only stdout",
			stdout:         "hello world",
			stderr:         "",
			exitCode:       0,
			interrupted:    false,
			exitCodeResult: ExitCodeResult{IsError: false},
			expectedParts:  []string{"hello world"},
		},
		{
			name:           "stdout and stderr",
			stdout:         "output",
			stderr:         "warning",
			exitCode:       0,
			interrupted:    false,
			exitCodeResult: ExitCodeResult{IsError: false},
			expectedParts:  []string{"output", "warning"},
		},
		{
			name:           "with interruption",
			stdout:         "partial output",
			stderr:         "",
			exitCode:       130,
			interrupted:    true,
			exitCodeResult: ExitCodeResult{IsError: true, Message: "Command failed with exit code 130"},
			expectedParts:  []string{"partial output", "Command was aborted before completion", "Exit code 130"},
		},
		{
			name:           "grep no matches",
			stdout:         "",
			stderr:         "",
			exitCode:       1,
			interrupted:    false,
			exitCodeResult: ExitCodeResult{IsError: false, Message: "No matches found"},
			expectedParts:  []string{}, // 没有输出，因为 stdout 和 stderr 都为空
		},
		{
			name:           "command error",
			stdout:         "",
			stderr:         "command not found",
			exitCode:       127,
			interrupted:    false,
			exitCodeResult: ExitCodeResult{IsError: true, Message: "Command failed with exit code 127"},
			expectedParts:  []string{"command not found", "Exit code 127"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tool.buildResponse(tt.stdout, tt.stderr, tt.exitCode, tt.interrupted, tt.exitCodeResult)

			if len(tt.expectedParts) == 0 {
				// 对于空期望，检查结果是否为空或只包含空白字符
				if strings.TrimSpace(result) != "" {
					t.Errorf("Expected empty result, got '%s'", result)
				}
			} else {
				for _, expectedPart := range tt.expectedParts {
					if !strings.Contains(result, expectedPart) {
						t.Errorf("Expected response to contain '%s', got '%s'", expectedPart, result)
					}
				}
			}
		})
	}
}

func TestBashTool_Run_InvalidParameters(t *testing.T) {
	mockPermissions := &mockBashPermissionService{shouldApprove: true}
	tool := NewBashTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	tests := []struct {
		name        string
		input       string
		expectedErr string
	}{
		{
			name:        "invalid JSON",
			input:       "invalid json",
			expectedErr: "invalid parameters",
		},
		{
			name:        "missing command",
			input:       `{"timeout": 5000}`,
			expectedErr: "missing command",
		},
		{
			name:        "empty command",
			input:       `{"command": "", "timeout": 5000}`,
			expectedErr: "missing command",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			call := tools.ToolCall{
				Id:    "test-call-789",
				Name:  tools.BashToolName,
				Input: tt.input,
			}

			response, err := tool.Run(toolCtx, call)
			if err != nil {
				t.Fatalf("Expected no error, got %v", err)
			}

			if !response.IsError {
				t.Error("Expected error response")
			}

			if !strings.Contains(response.Content, tt.expectedErr) {
				t.Errorf("Expected error message to contain '%s', got '%s'", tt.expectedErr, response.Content)
			}
		})
	}
}

func TestBashTool_Run_BannedCommands(t *testing.T) {
	// This test is disabled because command banning is not currently implemented
	// The bash tool relies on user permission system for command approval
	t.Skip("Command banning not currently implemented - using permission system instead")
}

func TestBashTool_Run_BasicCommands(t *testing.T) {
	tempDir, cleanup := setupTestDir(t)
	defer cleanup()

	mockPermissions := &mockBashPermissionService{shouldApprove: true}
	tool := NewBashTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	tests := []struct {
		name           string
		command        string
		workingDir     string
		expectError    bool
		expectedOutput []string
	}{
		{
			name:           "echo command",
			command:        "echo 'Hello, World!'",
			workingDir:     tempDir,
			expectError:    false,
			expectedOutput: []string{"Hello, World!"},
		},
		{
			name:           "list files",
			command:        "ls -la",
			workingDir:     tempDir,
			expectError:    false,
			expectedOutput: []string{"test.txt", "empty.txt", "data.json", "script.sh"},
		},
		{
			name:           "cat file",
			command:        "cat test.txt",
			workingDir:     tempDir,
			expectError:    false,
			expectedOutput: []string{"Hello World", "This is a test file"},
		},
		{
			name:           "pwd command",
			command:        "pwd",
			workingDir:     tempDir,
			expectError:    false,
			expectedOutput: []string{tempDir},
		},
		{
			name:           "grep with matches",
			command:        "grep 'Hello' test.txt",
			workingDir:     tempDir,
			expectError:    false,
			expectedOutput: []string{"Hello World"},
		},
		{
			name:           "grep no matches",
			command:        "grep 'nonexistent' test.txt",
			workingDir:     tempDir,
			expectError:    false,
			expectedOutput: []string{"No matches found"}, // 如果返回"no output"，我们需要调整期望
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := specs.BashParams{
				Command: "cd " + tt.workingDir + " && " + tt.command,
				Timeout: 5000,
			}

			input, err := json.Marshal(params)
			if err != nil {
				t.Fatalf("Failed to marshal params: %v", err)
			}

			call := tools.ToolCall{
				Id:    "test-call-789",
				Name:  tools.BashToolName,
				Input: string(input),
			}

			response, err := tool.Run(toolCtx, call)
			if err != nil {
				t.Fatalf("Expected no error, got %v", err)
			}

			if response.IsError != tt.expectError {
				t.Errorf("Expected error %v, got %v. Response: %s", tt.expectError, response.IsError, response.Content)
			}

			// 对于grep no matches的特殊处理
			if tt.name == "grep no matches" {
				// 检查是否包含"No matches found"或者是否为空/无输出
				if !strings.Contains(response.Content, "No matches found") &&
					response.Content != "no output" &&
					strings.TrimSpace(response.Content) != "" {
					t.Errorf("Expected 'No matches found' or no output, got '%s'", response.Content)
				}
			} else {
				for _, expectedOutput := range tt.expectedOutput {
					if !strings.Contains(response.Content, expectedOutput) {
						t.Errorf("Expected output to contain '%s', got '%s'", expectedOutput, response.Content)
					}
				}
			}
		})
	}
}

func TestBashTool_Run_TimeoutHandling(t *testing.T) {
	// 只测试参数处理逻辑，不需要实际的工具实例

	tests := []struct {
		name            string
		inputTimeout    int
		expectedTimeout int
	}{
		{
			name:            "no timeout specified",
			inputTimeout:    0,
			expectedTimeout: DefaultTimeout,
		},
		{
			name:            "valid timeout",
			inputTimeout:    5000,
			expectedTimeout: 5000,
		},
		{
			name:            "timeout exceeds maximum",
			inputTimeout:    MaxTimeout + 1000,
			expectedTimeout: MaxTimeout,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := specs.BashParams{
				Command: "echo test",
				Timeout: tt.inputTimeout,
			}

			// 这里我们主要测试超时值的处理逻辑
			// 实际的shell执行需要mock，所以我们只测试参数处理
			if params.Timeout > MaxTimeout {
				params.Timeout = MaxTimeout
			} else if params.Timeout <= 0 {
				params.Timeout = DefaultTimeout
			}

			if params.Timeout != tt.expectedTimeout {
				t.Errorf("Expected timeout %d, got %d", tt.expectedTimeout, params.Timeout)
			}
		})
	}
}

func TestBashTool_Run_PermissionDenied(t *testing.T) {
	mockPermissions := &mockBashPermissionService{shouldApprove: false}
	tool := NewBashTool(mockPermissions)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	// 使用一个需要权限的命令（不在safeReadOnlyCommands列表中）
	params := specs.BashParams{
		Command: "touch test_file.txt", // touch命令不在安全列表中，需要权限
		Timeout: 5000,
	}

	input, err := json.Marshal(params)
	if err != nil {
		t.Fatalf("Failed to marshal params: %v", err)
	}

	call := tools.ToolCall{
		Id:    "test-call-789",
		Name:  tools.BashToolName,
		Input: string(input),
	}

	_, err = tool.Run(toolCtx, call)

	// 权限被拒绝时应该返回permission.ErrorPermissionDenied错误
	if err == nil {
		t.Error("Expected permission denied error but got none")
		return
	}

	if err.Error() != "permission denied" {
		t.Errorf("Expected 'permission denied' error, got '%v'", err)
	}
}

func TestBashTool_SnapshotFunctionality(t *testing.T) {
	tempDir, cleanup := setupTestDir(t)
	defer cleanup()

	mockPermissions := &mockBashPermissionService{shouldApprove: true}
	tool := NewBashTool(mockPermissions).(*bashTool)

	toolCtx := tools.NewToolExecutionContextBuilder(context.Background()).
		WithSessionID("test-session-123").
		WithMessageID("test-message-456").
		Build()

	t.Run("test environment variable persistence", func(t *testing.T) {
		// 设置环境变量
		params1 := specs.BashParams{
			Command: "cd " + tempDir + " && export TEST_VAR=hello_world",
			Timeout: 5000,
		}

		input1, err := json.Marshal(params1)
		if err != nil {
			t.Fatalf("Failed to marshal params: %v", err)
		}

		call1 := tools.ToolCall{
			Id:    "test-call-1",
			Name:  tools.BashToolName,
			Input: string(input1),
		}

		// 执行第一个命令
		response1, err := tool.Run(toolCtx, call1)
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if response1.IsError {
			t.Errorf("First command failed: %s", response1.Content)
		}

		// 在新命令中使用之前设置的环境变量
		params2 := specs.BashParams{
			Command: "cd " + tempDir + " && echo $TEST_VAR",
			Timeout: 5000,
		}

		input2, err := json.Marshal(params2)
		if err != nil {
			t.Fatalf("Failed to marshal params: %v", err)
		}

		call2 := tools.ToolCall{
			Id:    "test-call-2",
			Name:  tools.BashToolName,
			Input: string(input2),
		}

		// 执行第二个命令
		response2, err := tool.Run(toolCtx, call2)
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if response2.IsError {
			t.Errorf("Second command failed: %s", response2.Content)
		}

		// 验证环境变量是否持续存在
		if !strings.Contains(response2.Content, "hello_world") {
			t.Errorf("Environment variable was not persisted. Got: %s", response2.Content)
		}
	})

	t.Run("test function definition persistence", func(t *testing.T) {
		// 定义一个函数
		params1 := specs.BashParams{
			Command: "cd " + tempDir + " && my_test_function() { echo 'function works'; }",
			Timeout: 5000,
		}

		input1, err := json.Marshal(params1)
		if err != nil {
			t.Fatalf("Failed to marshal params: %v", err)
		}

		call1 := tools.ToolCall{
			Id:    "test-call-1",
			Name:  tools.BashToolName,
			Input: string(input1),
		}

		// 执行第一个命令
		response1, err := tool.Run(toolCtx, call1)
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if response1.IsError {
			t.Errorf("Function definition failed: %s", response1.Content)
		}

		// 在新命令中调用之前定义的函数
		params2 := specs.BashParams{
			Command: "cd " + tempDir + " && my_test_function",
			Timeout: 5000,
		}

		input2, err := json.Marshal(params2)
		if err != nil {
			t.Fatalf("Failed to marshal params: %v", err)
		}

		call2 := tools.ToolCall{
			Id:    "test-call-2",
			Name:  tools.BashToolName,
			Input: string(input2),
		}

		// 执行第二个命令
		response2, err := tool.Run(toolCtx, call2)
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if response2.IsError {
			t.Errorf("Function call failed: %s", response2.Content)
		}

		// 验证函数是否持续存在
		if !strings.Contains(response2.Content, "function works") {
			t.Errorf("Function was not persisted. Got: %s", response2.Content)
		}
	})

	// 清理快照文件
	defer tool.cleanupSnapshot()
}

func TestBashTool_SnapshotFileOperations(t *testing.T) {
	mockPermissions := &mockBashPermissionService{shouldApprove: true}
	tool := NewBashTool(mockPermissions).(*bashTool)

	t.Run("test createSnapshotFile", func(t *testing.T) {
		err := tool.createSnapshotFile()
		if err != nil {
			t.Errorf("Failed to create snapshot file: %v", err)
		}

		// 验证文件是否存在
		if _, err := os.Stat(tool.snapshotFile); os.IsNotExist(err) {
			t.Error("Snapshot file was not created")
		}
	})

	t.Run("test generateShellSnapshot", func(t *testing.T) {
		script := tool.generateShellSnapshot()
		if script == "" {
			t.Error("Generated snapshot script is empty")
		}

		// 验证脚本包含关键部分
		expectedParts := []string{
			"# Shell Functions",
			"# Shell Aliases",
			"# Shell Options",
			"# Environment Variables",
			"# Working Directory",
		}

		for _, part := range expectedParts {
			if !strings.Contains(script, part) {
				t.Errorf("Snapshot script missing part: %s", part)
			}
		}
	})

	t.Run("test restoreSnapshot", func(t *testing.T) {
		// 首先创建快照文件
		err := tool.createSnapshotFile()
		if err != nil {
			t.Fatalf("Failed to create snapshot file: %v", err)
		}

		// 写入一些测试内容
		testContent := "export TEST_RESTORE=works\necho 'snapshot restored'"
		err = os.WriteFile(tool.snapshotFile, []byte(testContent), 0644)
		if err != nil {
			t.Fatalf("Failed to write test content: %v", err)
		}

		// 测试恢复快照
		restoreCmd := tool.restoreSnapshot()
		if restoreCmd == "" {
			t.Error("Restore command is empty")
		}

		if !strings.Contains(restoreCmd, "source") {
			t.Error("Restore command should contain 'source'")
		}
	})

	t.Run("test wrapCommandWithSnapshot", func(t *testing.T) {
		testCommand := "echo 'test command'"
		wrappedCommand := tool.wrapCommandWithSnapshot(testCommand)

		if !strings.Contains(wrappedCommand, testCommand) {
			t.Error("Wrapped command should contain original command")
		}

		// 如果没有快照文件，应该返回原命令
		if wrappedCommand == testCommand {
			// 这是预期的，如果没有快照文件
		} else {
			// 如果有快照文件，应该包含恢复逻辑
			if !strings.Contains(wrappedCommand, "# Restore previous shell state") {
				t.Error("Wrapped command should contain restore logic")
			}
		}
	})

	// 清理快照文件
	defer tool.cleanupSnapshot()
}
