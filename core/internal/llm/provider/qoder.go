package provider

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/openai/openai-go"
	"github.com/qoder-ai/qoder-cli/core/llm/provider"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/qoder"
	"github.com/qoder-ai/qoder-cli/qoder/sse"
	"time"
)

type QoderClient client

var lastSig string

type qoderClient struct {
	options   *provider.ClientBuilder
	model     string
	reasoning bool
}

func newQoderClient(opts *provider.ClientBuilder) QoderClient {
	model := opts.Model.APIModel
	reasoning := opts.Model.CanReason

	return &qoderClient{
		options:   opts,
		model:     model,
		reasoning: reasoning,
	}
}

func (o *qoderClient) convertMessages(messages []message.Message) []*qoder.Message {
	var qoderMessages []*qoder.Message
	qoderMessages = append(qoderMessages, &qoder.Message{
		Role:    qoder.RoleTypeSystem,
		Content: o.options.SystemMessage,
	})

	for _, msg := range messages {
		switch msg.Role {
		case message.User:
			m := &qoder.Message{}
			m.Role = qoder.RoleTypeUser
			m.Content = msg.Content().String()
			qoderMessages = append(qoderMessages, m)
		case message.Assistant:
			m := &qoder.Message{}
			m.Role = qoder.RoleTypeAssistant
			m.Content = msg.Content().String()
			m.ReasoningContent = msg.ReasoningContent().String()
			// fixme 先写死
			m.ReasoningContentSignature = lastSig

			if len(msg.ToolCalls()) > 0 {
				for _, call := range msg.ToolCalls() {
					tc := qoder.ToolCall{}
					tc.Id = call.Id
					tc.Type = "function"
					tc.Function = qoder.FunctionCall{
						Name:      call.Name,
						Arguments: call.Input,
					}
					m.ToolCalls = append(m.ToolCalls, tc)
				}
			}

			qoderMessages = append(qoderMessages, m)
		case message.Tool:
			for _, result := range msg.ToolResults() {
				m := &qoder.Message{}
				m.Role = qoder.RoleTypeTool
				m.Content = result.Content
				m.ToolCallId = result.ToolCallId
				qoderMessages = append(qoderMessages, m)
			}
		}
	}

	return qoderMessages
}

func (o *qoderClient) convertTools(tools []tools.BaseTool) []qoder.Tool {
	var qoderTools []qoder.Tool

	for _, tool := range tools {
		info := tool.Info()

		t := qoder.Tool{}
		t.Type = "function"
		t.Function = &qoder.FunctionDefinition{
			Name:        info.Name,
			Description: info.Description,
			Parameters: openai.FunctionParameters{
				"type":       "object",
				"properties": info.Parameters,
				"required":   info.Required,
			},
			Strict: false,
		}

		qoderTools = append(qoderTools, t)
	}

	return qoderTools
}

func (o *qoderClient) finishReason(reason string) message.FinishReason {
	switch reason {
	case "stop":
		return message.FinishReasonEndTurn
	case "length":
		return message.FinishReasonMaxTokens
	case "tool_calls":
		return message.FinishReasonToolUse
	default:
		return message.FinishReasonUnknown
	}
}

func (o *qoderClient) send(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (response *provider.Response, err error) {
	ch := o.stream(ctx, messages, tools)
	var pr *provider.Response
	for pe := range ch {
		pr = pe.Response
	}

	return pr, nil
}

func (o *qoderClient) stream(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event {
	// 组装请求
	ask := &qoder.RemoteChatAsk{}
	var sessionId string
	var requestId string

	if messages[0].SessionId == "" {
		sessionId = uuid.NewString()
	} else {
		sessionId = messages[0].SessionId
	}

	requestId = uuid.NewString()
	ask.SessionId = sessionId
	ask.RequestId = requestId
	ask.RequestSetId = requestId
	ask.ChatRecordId = requestId

	ask.Stream = true
	ask.ChatTask = "FREE_INPUT"
	ask.ChatContext = map[string]interface{}{
		"text":               messages[len(messages)-1].Content(),
		"workspaceLanguages": []string{"java"},
		"language":           "",
		"localeLang":         "zh-cn",
		"preferredLanguage":  "zh-cn",
		"features":           []string{},
		"extra": map[string]interface{}{
			"context": []string{},
			"modelConfig": map[string]interface{}{
				"key":          o.model,
				"is_reasoning": o.reasoning,
			},
			"originalContent": messages[len(messages)-1].Content(),
		},
		"chatPrompt": "",
		"imageUrls":  nil,
	}
	ask.IsReply = true
	ask.Source = 1
	ask.Version = "3"
	ask.UserType = "personal_standard"
	ask.SessionType = "assistant"
	ask.AgentId = "agent_common"
	ask.TaskId = "common"
	ask.ModelConfig = qoder.ModelConfig{
		Key:            o.model,
		DisplayName:    o.model,
		Model:          "",
		Format:         "openai",
		IsVl:           false,
		IsReasoning:    o.reasoning,
		ApiKey:         "",
		Url:            "",
		Source:         "system",
		MaxInputTokens: 200000,
	}

	ask.Messages = o.convertMessages(messages)
	ask.Tools = o.convertTools(tools)

	eventChan := make(chan provider.Event)

	// 发送请求
	req, err := qoder.BuildRequest(ask, nil)
	if err != nil {
		logging.Error("Fail to build qoder request", "err", err)
		return nil
	}

	logging.Info("Requesting: " + req.URL.String())
	logging.Info("RequestId: " + requestId)

	go func() {
		agentResponse := &qoder.CommonAgentResponse{}
		pe := provider.Event{}
		pr := &provider.Response{}

		err = sse.NewClient("POST", nil).Subscribe(req, time.Second*100, func(msg *sse.Event) {
			logging.Info("LLM Data: " + string(msg.Data))
			logging.Info("LLM Event: " + string(msg.Event))
			var response qoder.ChatResponse
			if string(msg.Event) == "error" {
				agentResponse.Err = fmt.Errorf("execute agent request error")
				logging.Error("execute agent request error")

				eventChan <- provider.Event{
					Type: provider.EventError, Error: agentResponse.Err,
				}
				close(eventChan)
				return
			}

			err := json.Unmarshal(msg.Data, &response)
			if err != nil {
				agentResponse.Err = fmt.Errorf("execute agent request unmarshal response error. error: %v, data: %s", err, msg.Data)

				eventChan <- provider.Event{
					Type: provider.EventError, Error: agentResponse.Err,
				}
				close(eventChan)
				return
			}

			//if string(msg.Event) == "finish" {
			//	return
			//}

			if response.StatusCodeValue != 200 && string(msg.Event) != "finish" {
				m := response.Body
				agentResponse.Err = fmt.Errorf("execute agent request finished unexpected reason: %s, statusCode: %v", m, response.StatusCode)

				eventChan <- provider.Event{
					Type: provider.EventError, Error: agentResponse.Err,
				}
				close(eventChan)
				return
			}

			bodyData := response.Body
			if bodyData == "[DONE]" {
				eventChan <- provider.Event{
					Type:     provider.EventComplete,
					Response: pr,
				}
				close(eventChan)
				return
			}

			var streamResponse qoder.StreamedChatResponsePayload
			err = json.NewDecoder(bytes.NewReader([]byte(bodyData))).Decode(&streamResponse)
			if err != nil {
				logging.Error("failed to decode stream payload", "err", err)
				return
			}
			agentResponse.RequestId = streamResponse.Id

			if streamResponse.Error != nil {
				agentResponse.Err = err
				return
			}

			if streamResponse.Usage != nil {
				pr.Usage.OutputTokens = int64(streamResponse.Usage.CompletionTokens)
				pr.Usage.InputTokens = int64(streamResponse.Usage.PromptTokens)
			}

			if len(streamResponse.Choices) == 0 {
				return
			}
			choice := streamResponse.Choices[0]
			if choice.Delta.Content != "" {
				eventChan <- provider.Event{
					Type:    provider.EventContentDelta,
					Content: choice.Delta.Content,
				}
				pr.Content += choice.Delta.Content
			}

			if choice.Delta.ReasoningContent != "" {
				eventChan <- provider.Event{
					Type:    provider.EventThinkingDelta,
					Content: choice.Delta.ReasoningContent,
				}
				pe.Thinking += choice.Delta.ReasoningContent
			}

			if choice.FinishReason != "" {
				pr.FinishReason = o.finishReason(string(choice.FinishReason))
			}

			if choice.Delta.Signature != "" {
				lastSig = choice.Delta.Signature
			}

			if len(choice.Delta.ToolCalls) > 0 {
				ti := len(pr.ToolCalls) - 1
				call := choice.Delta.ToolCalls[0]
				if call.Id == "" {
					// lingma server可能有bug，在不返回id的情况下直接开始返回参数，这里先兼容下，少一个tool call 等后面的对话补偿
					if ti >= 0 {
						pr.ToolCalls[ti].Input = pr.ToolCalls[ti].Input + call.Function.Arguments
					}
				} else {
					pr.ToolCalls = append(pr.ToolCalls, message.ToolCall{
						Id:       call.Id,
						Name:     call.Function.Name,
						Input:    call.Function.Arguments,
						Type:     call.Type,
						Finished: true,
					})
				}

			}
		}, func() {
			logging.Error("request timed out", "requestId", requestId)
		})

		if err != nil {
			logging.Error("Fail to build qoder request", "err", err)
			eventChan <- provider.Event{
				Type: provider.EventError, Error: err,
			}
			close(eventChan)
			return
		}
	}()

	return eventChan
}

func (o *qoderClient) usage(completion openai.ChatCompletion) provider.TokenUsage {
	cachedTokens := completion.Usage.PromptTokensDetails.CachedTokens
	inputTokens := completion.Usage.PromptTokens - cachedTokens

	return provider.TokenUsage{
		InputTokens:         inputTokens,
		OutputTokens:        completion.Usage.CompletionTokens,
		CacheCreationTokens: 0, // OpenAI doesn't provide this directly
		CacheReadTokens:     cachedTokens,
	}
}
