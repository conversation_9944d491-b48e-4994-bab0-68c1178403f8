package provider

import (
	"context"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/llm/provider"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/message"
)

const maxRetries = 8

type client interface {
	send(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*provider.Response, error)
	stream(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event
}

type baseClient[C client] struct {
	client  C
	options *provider.ClientBuilder
}

func (c *baseClient[C]) cleanMessages(messages []message.Message) (cleaned []message.Message) {
	for _, msg := range messages {
		// The message has no content
		if len(msg.Parts) == 0 {
			continue
		}
		cleaned = append(cleaned, msg)
	}
	return
}

func (c *baseClient[C]) SendMessages(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*provider.Response, error) {
	messages = c.cleanMessages(messages)
	return c.client.send(ctx, messages, tools)
}

func (c *baseClient[C]) StreamResponse(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan provider.Event {
	messages = c.cleanMessages(messages)
	return c.client.stream(ctx, messages, tools)
}

func (c *baseClient[C]) GetModel() models.Model {
	return c.options.Model
}

type service struct {
}

func (s *service) NewClient(providerName models.ModelProvider, opts ...provider.ClientOption) (provider.Client, error) {
	builder := &provider.ClientBuilder{}
	for _, o := range opts {
		o(builder)
	}

	switch providerName {
	case models.ProviderIdeaLab:
		return &baseClient[IdeaLabClient]{
			options: builder,
			client:  newIdeaLabClient(builder),
		}, nil
	case models.ProviderDashScope:
		builder.BaseUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1"
		return &baseClient[OpenAIClient]{
			options: builder,
			client:  newOpenAIClient(builder),
		}, nil
	case models.ProviderQoder:
		return &baseClient[QoderClient]{
			options: builder,
			client:  newQoderClient(builder),
		}, nil
	default:
		panic("not implemented")
	}
}

func NewService() provider.Service {
	return &service{}
}
