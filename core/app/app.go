package app

import (
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/agent"
	"github.com/qoder-ai/qoder-cli/core/llm/provider"
	"github.com/qoder-ai/qoder-cli/core/message"
)

type App interface {
	GetConfig() *config.Config
	AgentManager
	ServiceContainer
}

type AgentManager interface {
	GetCoderAgent() agent.Agent
	GetReviewerAgent() agent.Agent
}

type ServiceContainer interface {
	GetMessageService() message.MessageService
	GetProjectService() core.ProjectService
	GetSessionService() core.SessionService
	GetProviderService() provider.Service
	GetHistoryService() core.HistoryService
}
