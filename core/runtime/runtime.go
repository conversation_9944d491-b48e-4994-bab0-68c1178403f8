package runtime

import (
	"context"
	"errors"
	"fmt"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/internal"
	"github.com/qoder-ai/qoder-cli/core/internal/llm/agent"
	"github.com/qoder-ai/qoder-cli/core/internal/llm/provider"
	coreAgent "github.com/qoder-ai/qoder-cli/core/llm/agent"
	coreProv "github.com/qoder-ai/qoder-cli/core/llm/provider"
	"github.com/qoder-ai/qoder-cli/core/logging"
	coreMsg "github.com/qoder-ai/qoder-cli/core/message"
)

type AppRuntime struct {
	*lspManager
	config        *config.Config
	sessions      core.SessionService
	messages      coreMsg.MessageService
	histories     core.HistoryService
	projects      core.ProjectService
	providers     coreProv.Service
	permissions   core.PermissionService
	coderAgent    coreAgent.Agent
	reviewerAgent coreAgent.Agent
}

func NewAppRuntime(ctx context.Context, cfg *config.Config) (*AppRuntime, error) {
	projects := internal.NewProjectService(cfg.WorkingDir)
	sessions := internal.NewSessionService(projects)
	messages := internal.NewMessageService(projects)
	files := internal.NewHistoryService(projects)
	providers := provider.NewService()

	runtime := &AppRuntime{
		sessions:    sessions,
		messages:    messages,
		histories:   files,
		projects:    projects,
		providers:   providers,
		permissions: internal.NewPermissionService(),
		lspManager:  newLspManager(cfg),
		config:      cfg,
	}

	go runtime.initLspClients(ctx)

	var err error
	runtime.coderAgent, err = agent.NewAgent(config.AgentCoder, runtime,
		agent.CoderAgentTools(runtime, runtime.permissions))
	if err != nil {
		logging.Error("Failed to create coder agent", err)
		return nil, err
	}

	runtime.reviewerAgent, err = agent.NewAgent(config.AgentReviewer, runtime,
		agent.ReviewerTools(runtime.permissions))
	if err != nil {
		logging.Error("Failed to create reviewer agent", err)
		return nil, err
	}

	return runtime, nil
}

func (a *AppRuntime) GetConfig() *config.Config {
	return a.config
}

func (a *AppRuntime) RunNonInteractive(ctx context.Context, prompt string, sessionID string) error {
	logging.Info("Running in non-interactive mode")

	const maxPromptLengthForTitle = 100
	titlePrefix := "Non-interactive: "
	var titleSuffix string

	if len(prompt) > maxPromptLengthForTitle {
		titleSuffix = prompt[:maxPromptLengthForTitle] + "..."
	} else {
		titleSuffix = prompt
	}
	title := titlePrefix + titleSuffix

	var sess core.Session
	var err error
	if sessionID != "" {
		sess, err = a.GetSessionService().Get(ctx, sessionID)
		if err != nil {
			return fmt.Errorf("failed to get session for non-interactive mode: %w", err)
		}
		logging.Info("Get session for non-interactive run", "session_id", sess.Id)
	} else {
		cfg := &core.SessionConfig{
			WorkingDir:   a.config.WorkingDir,
			ContextPaths: a.config.ContextPaths,
		}
		sess, err = a.GetSessionService().CreateWithConfig(ctx, title, cfg)
		if err != nil {
			return fmt.Errorf("failed to create session for non-interactive mode: %w", err)
		}
		logging.Info("Created session for non-interactive run", "session_id", sess.Id)
	}

	a.permissions.AutoApproveSession(sess.Id)

	done, err := a.coderAgent.Run(ctx, sess.Id, prompt)
	if err != nil {
		return fmt.Errorf("failed to start agent processing stream: %w", err)
	}

	result := <-done
	if result.Error != nil {
		if errors.Is(result.Error, context.Canceled) || errors.Is(result.Error, coreAgent.ErrRequestCancelled) {
			logging.Info("Agent processing cancelled", "session_id", sess.Id)
			return nil
		}
		return fmt.Errorf("agent processing failed: %w", result.Error)
	}

	logging.Info("Non-interactive run completed", "session_id", sess.Id)
	return nil
}

func (a *AppRuntime) GetCoderAgent() coreAgent.Agent {
	return a.coderAgent
}

func (a *AppRuntime) GetMessageService() coreMsg.MessageService {
	return a.messages
}

func (a *AppRuntime) GetProjectService() core.ProjectService {
	return a.projects
}

func (a *AppRuntime) GetSessionService() core.SessionService {
	return a.sessions
}

func (a *AppRuntime) GetProviderService() coreProv.Service {
	return a.providers
}

func (a *AppRuntime) GetHistoryService() core.HistoryService {
	return a.histories
}

func (a *AppRuntime) GetPermissionService() core.PermissionService {
	return a.permissions
}

func (a *AppRuntime) GetReviewerAgent() coreAgent.Agent {
	return a.reviewerAgent
}
