package runtime

import (
	"context"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/lsp"
	"github.com/qoder-ai/qoder-cli/lsp/watcher"
	"maps"
	"sync"
	"time"
)

type lspManager struct {
	lspClient          map[string]*lsp.Client
	clientsMutex       sync.RWMutex
	watcherCancelFuncs []context.CancelFunc
	cancelFuncsMutex   sync.Mutex
	watcherWG          sync.WaitGroup
	config             *config.Config
}

func newLspManager(config *config.Config) *lspManager {
	return &lspManager{
		lspClient: make(map[string]*lsp.Client),
		config:    config,
	}
}

func (a *lspManager) initLspClients(ctx context.Context) {
	lspConfig := lsp.ConfigFromApp(a.config)

	for name, clientConfig := range lspConfig.ClientConfigs {
		go a.createAndStartLspClient(ctx, lspConfig, name, clientConfig.Command, clientConfig.Args...)
	}

	logging.Info("LSP clients initialization started in background")
}

func (a *lspManager) createAndStartLspClient(ctx context.Context, lspConfig *lsp.Config, name string, command string, args ...string) {
	logging.Info("Creating LSP client", "name", name, "command", command, "args", args)
	lspClient, err := lsp.NewClient(ctx, lspConfig, command, args...)
	if err != nil {
		logging.Error("Failed to create LSP client for", name, err)
		return
	}

	initCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	_, err = lspClient.InitializeLspClient(initCtx, lspConfig.WorkingDirectory)
	if err != nil {
		logging.Error("Initialize failed", "name", name, "error", err)
		lspClient.Close()
		return
	}

	if err := lspClient.WaitForServerReady(initCtx); err != nil {
		logging.Error("Server failed to become ready", "name", name, "error", err)
		lspClient.SetServerState(lsp.StateError)
	} else {
		logging.Info("LSP server is ready", "name", name)
		lspClient.SetServerState(lsp.StateReady)
	}

	logging.Info("LSP client initialized", "name", name)

	watchCtx, cancelFunc := context.WithCancel(ctx)
	watchCtx = context.WithValue(watchCtx, "serverName", name)
	workspaceWatcher := watcher.NewWorkspaceWatcher(lspClient, lspConfig.DebugLsp)

	a.cancelFuncsMutex.Lock()
	a.watcherCancelFuncs = append(a.watcherCancelFuncs, cancelFunc)
	a.cancelFuncsMutex.Unlock()

	a.watcherWG.Add(1)

	a.clientsMutex.Lock()
	a.lspClient[name] = lspClient
	a.clientsMutex.Unlock()

	go a.runWorkspaceWatcher(watchCtx, name, workspaceWatcher, lspConfig)
}

func (a *lspManager) runWorkspaceWatcher(ctx context.Context, name string, workspaceWatcher *watcher.WorkspaceWatcher, lspConfig *lsp.Config) {
	defer a.watcherWG.Done()
	defer logging.RecoverPanic("LSP-"+name, func() {
		a.restartLspClient(ctx, name)
	})

	workspaceWatcher.WatchWorkspace(ctx, lspConfig.WorkingDirectory)
	logging.Info("Workspace watcher stopped", "client", name)
}

func (a *lspManager) restartLspClient(ctx context.Context, name string) {
	lspConfig := lsp.ConfigFromApp(a.config)
	clientConfig, exists := lspConfig.ClientConfigs[name]
	if !exists {
		logging.Error("Cannot restart client, configuration not found", "client", name)
		return
	}

	a.clientsMutex.Lock()
	oldClient, exists := a.lspClient[name]
	if exists {
		delete(a.lspClient, name)
	}
	a.clientsMutex.Unlock()

	if exists && oldClient != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		_ = oldClient.Shutdown(shutdownCtx)
		cancel()
	}

	a.createAndStartLspClient(ctx, lspConfig, name, clientConfig.Command, clientConfig.Args...)
	logging.Info("Successfully restarted LSP client", "client", name)
}

func (a *lspManager) GetLspClients() map[string]*lsp.Client {
	return a.lspClient
}

func (a *lspManager) Shutdown() {
	a.cancelFuncsMutex.Lock()
	for _, cancel := range a.watcherCancelFuncs {
		cancel()
	}
	a.cancelFuncsMutex.Unlock()
	a.watcherWG.Wait()
	a.clientsMutex.RLock()
	clients := make(map[string]*lsp.Client, len(a.lspClient))
	maps.Copy(clients, a.lspClient)
	a.clientsMutex.RUnlock()

	for name, client := range clients {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		if err := client.Shutdown(shutdownCtx); err != nil {
			logging.Error("Failed to shutdown LSP client", "name", name, "error", err)
		}
		cancel()
	}
}
