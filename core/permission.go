package core

import (
	"errors"

	"github.com/qoder-ai/qoder-cli/core/pubsub"
)

var ErrorPermissionDenied = errors.New("permission denied")

type CreatePermissionRequest struct {
	SessionId   string `json:"session_id"`
	ToolName    string `json:"tool_name"`
	Description string `json:"description"`
	Action      string `json:"action"`
	Params      any    `json:"params"`
	Path        string `json:"path"`
}

type PermissionRequest struct {
	Id          string `json:"id"`
	SessionId   string `json:"session_id"`
	ToolName    string `json:"tool_name"`
	Description string `json:"description"`
	Action      string `json:"action"`
	Params      any    `json:"params"`
	Path        string `json:"path"`
}

type PermissionService interface {
	pubsub.Subscriber[PermissionRequest]
	PermissionTrigger
	PermissionOperator
}

type SessionContext interface {
	GetSessionId() string
	GetWorkingDir() string
	GetContextPaths() []string
}

type PermissionTrigger interface {
	CreateRequestWithContext(ctx SessionContext, opts CreatePermissionRequest) bool
}

type PermissionOperator interface {
	GrantPersistent(permission PermissionRequest)
	Grant(permission PermissionRequest)
	Deny(permission PermissionRequest)
	AutoApproveSession(sessionId string)
}
