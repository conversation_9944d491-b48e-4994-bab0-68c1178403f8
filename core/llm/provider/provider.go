package provider

import (
	"context"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/message"
)

type EventType string

const (
	EventContentStart  EventType = "content_start"
	EventToolUseStart  EventType = "tool_use_start"
	EventToolUseDelta  EventType = "tool_use_delta"
	EventToolUseStop   EventType = "tool_use_stop"
	EventContentDelta  EventType = "content_delta"
	EventThinkingDelta EventType = "thinking_delta"
	EventContentStop   EventType = "content_stop"
	EventComplete      EventType = "complete"
	EventError         EventType = "error"
	EventWarning       EventType = "warning"
)

type ReasoningEffort string

const (
	ReasoningEffortLow    ReasoningEffort = "low"
	ReasoningEffortMedium ReasoningEffort = "medium"
	ReasoningEffortHigh   ReasoningEffort = "high"
)

type TokenUsage struct {
	InputTokens         int64
	OutputTokens        int64
	CacheCreationTokens int64
	CacheReadTokens     int64
}

type Response struct {
	Content      string
	Signature    string
	ToolCalls    []message.ToolCall
	Usage        TokenUsage
	FinishReason message.FinishReason
}

type Event struct {
	Type     EventType
	Content  string
	Thinking string
	Response *Response
	ToolCall *message.ToolCall
	Error    error
}

type Client interface {
	SendMessages(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*Response, error)
	StreamResponse(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan Event
	GetModel() models.Model
}

type Service interface {
	NewClient(name models.ModelProvider, option ...ClientOption) (Client, error)
}
