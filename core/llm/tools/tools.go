package tools

import (
	"context"
	"encoding/json"
	"github.com/qoder-ai/qoder-cli/core/message"
	"os"
)

const (
	ToolResponseTypeText    toolResponseType        = "text"
	ToolResponseTypeImage   toolResponseType        = "image"
	SessionIDContextKey     sessionIDContextKey     = "session_id"
	MessageIDContextKey     messageIDContextKey     = "message_id"
	ToolExecutionContextKey toolExecutionContextKey = "tool_execution_context"
)

type ToolInfo struct {
	Name        string
	Description string
	Parameters  map[string]any
	Required    []string
}

type ToolExecutionContext interface {
	context.Context
	GetSessionId() string
	GetMessageId() string
	GetWorkingDir() string
	GetContextPaths() []string
	GetStringConfig(key, defaultValue string) string
	GetBoolConfig(key string, defaultValue bool) bool
	GetIntConfig(key string, defaultValue int) int
	SetConfig(key string, value interface{})
}

type toolExecutionContext struct {
	context.Context
	sessionId    string
	messageId    string
	workingDir   string
	contextPaths []string
	config       map[string]interface{}
}

func (ctx *toolExecutionContext) GetSessionId() string {
	return ctx.sessionId
}

func (ctx *toolExecutionContext) GetMessageId() string {
	return ctx.messageId
}

func (ctx *toolExecutionContext) GetWorkingDir() string {
	if ctx.workingDir != "" {
		return ctx.workingDir
	}
	if wd, err := os.Getwd(); err == nil {
		return wd
	}
	return "."
}

func (ctx *toolExecutionContext) GetContextPaths() []string {
	return ctx.contextPaths
}

func (ctx *toolExecutionContext) GetStringConfig(key, defaultValue string) string {
	if ctx.config == nil {
		return defaultValue
	}
	if val, ok := ctx.config[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return defaultValue
}

func (ctx *toolExecutionContext) GetBoolConfig(key string, defaultValue bool) bool {
	if ctx.config == nil {
		return defaultValue
	}
	if val, ok := ctx.config[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return defaultValue
}

func (ctx *toolExecutionContext) GetIntConfig(key string, defaultValue int) int {
	if ctx.config == nil {
		return defaultValue
	}
	if val, ok := ctx.config[key]; ok {
		if i, ok := val.(int); ok {
			return i
		}
	}
	return defaultValue
}

func (ctx *toolExecutionContext) SetConfig(key string, value interface{}) {
	if ctx.config == nil {
		ctx.config = make(map[string]interface{})
	}
	ctx.config[key] = value
}

type toolResponseType string

type (
	sessionIDContextKey     string
	messageIDContextKey     string
	toolExecutionContextKey string
)

type ToolResponse struct {
	Type         toolResponseType `json:"type"`
	Content      string           `json:"content"`
	ImageContent string           `json:"image_content,omitempty"`
	MimeType     string           `json:"mime_type,omitempty"`
	Metadata     string           `json:"metadata,omitempty"`
	IsError      bool             `json:"is_error"`
}

func NewTextResponse(content string) ToolResponse {
	return ToolResponse{
		Type:    ToolResponseTypeText,
		Content: content,
	}
}

func NewImageResponse(mimeType string, base64Content string) ToolResponse {
	return ToolResponse{
		Type:         ToolResponseTypeImage,
		ImageContent: base64Content,
		MimeType:     mimeType,
	}
}

func WithResponseMetadata(response ToolResponse, metadata any) ToolResponse {
	if metadata != nil {
		metadataBytes, err := json.Marshal(metadata)
		if err != nil {
			return response
		}
		response.Metadata = string(metadataBytes)
	}
	return response
}

func NewTextErrorResponse(content string) ToolResponse {
	return ToolResponse{
		Type:    ToolResponseTypeText,
		Content: content,
		IsError: true,
	}
}

type ChatRequest func(ctx context.Context, messages []message.Message, tools []BaseTool) (string, error)

type ToolCall struct {
	ChatRequest ChatRequest `json:"-"`
	Id          string      `json:"id"`
	Name        string      `json:"name"`
	Input       string      `json:"input"`
}

type BaseTool interface {
	Info() ToolInfo
	Run(ctx ToolExecutionContext, params ToolCall) (ToolResponse, error)
}

type ToolExecutionContextBuilder struct {
	baseCtx      context.Context
	sessionId    string
	messageId    string
	workingDir   string
	contextPaths []string
	config       map[string]interface{}
}

func NewToolExecutionContextBuilder(baseCtx context.Context) *ToolExecutionContextBuilder {
	return &ToolExecutionContextBuilder{
		baseCtx: baseCtx,
		config:  make(map[string]interface{}),
	}
}

func (b *ToolExecutionContextBuilder) WithSessionID(sessionID string) *ToolExecutionContextBuilder {
	b.sessionId = sessionID
	return b
}

func (b *ToolExecutionContextBuilder) WithMessageID(messageID string) *ToolExecutionContextBuilder {
	b.messageId = messageID
	return b
}

func (b *ToolExecutionContextBuilder) WithWorkingDir(workingDir string) *ToolExecutionContextBuilder {
	b.workingDir = workingDir
	return b
}

func (b *ToolExecutionContextBuilder) WithContextPaths(contextPaths []string) *ToolExecutionContextBuilder {
	b.contextPaths = contextPaths
	return b
}

func (b *ToolExecutionContextBuilder) WithConfig(key string, value interface{}) *ToolExecutionContextBuilder {
	if b.config == nil {
		b.config = make(map[string]interface{})
	}
	b.config[key] = value
	return b
}

func (b *ToolExecutionContextBuilder) Build() ToolExecutionContext {
	return &toolExecutionContext{
		Context:      b.baseCtx,
		sessionId:    b.sessionId,
		messageId:    b.messageId,
		workingDir:   b.workingDir,
		contextPaths: b.contextPaths,
		config:       b.config,
	}
}
