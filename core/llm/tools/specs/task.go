package specs

import (
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/message"
)

var TaskSpec = tools.Spec[TaskParams, tools.NotPresent, TaskResult]{}

type TaskParams struct {
	Description  string `json:"description"`
	Prompt       string `json:"prompt"`
	SubagentType string `json:"subagent_type"`
}

func (t TaskParams) IsPresent() bool {
	return true
}

type TaskResult struct {
	Content           []message.ContentPart `json:"content"`
	TotalDurationMs   int64                 `json:"totalDurationMs"`
	TotalTokens       int                   `json:"totalTokens"`
	TotalToolUseCount int                   `json:"totalToolUseCount"`
}

func (t TaskResult) IsPresent() bool {
	return true
}
