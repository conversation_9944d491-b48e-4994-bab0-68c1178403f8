package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var BashSpec = tools.Spec[BashParams, BashPermissionsParams, BashResponseMetadata]{}

type BashParams struct {
	Command string `json:"command"`
	Timeout int    `json:"timeout"`
}

func (b BashParams) IsPresent() bool {
	return true
}

type BashPermissionsParams struct {
	Command string `json:"command"`
	Timeout int    `json:"timeout"`
}

func (b BashPermissionsParams) IsPresent() bool {
	return true
}

type BashResponseMetadata struct {
	StartTime   int64  `json:"start_time"`
	EndTime     int64  `json:"end_time"`
	ExitCode    int    `json:"exit_code"`
	IsImage     bool   `json:"is_image"`
	ExitMessage string `json:"exit_message,omitempty"`
}

func (b BashResponseMetadata) IsPresent() bool {
	return true
}
