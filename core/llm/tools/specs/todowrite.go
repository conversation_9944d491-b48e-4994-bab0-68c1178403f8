package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var TodoWriteSpec = tools.Spec[TodoWriteParams, tools.NotPresent, TodoWriteResult]{}

type TodoPriority string

const (
	TodoPriorityHigh   TodoPriority = "high"
	TodoPriorityMedium TodoPriority = "medium"
	TodoPriorityLow    TodoPriority = "low"
)

type TodoStatus string

const (
	TodoStatusPending    TodoStatus = "pending"
	TodoStatusInProgress TodoStatus = "in_progress"
	TodoStatusCompleted  TodoStatus = "completed"
)

type TodoWriteResult struct {
	OldTodos []Todo `json:"oldTodos"`
	NewTodos []Todo `json:"newTodos"`
}

func (t TodoWriteResult) IsPresent() bool {
	return true
}

type Todo struct {
	Content  string       `json:"content"`
	Status   TodoStatus   `json:"status"`
	Priority TodoPriority `json:"priority"`
	ID       string       `json:"id"`
}

type TodoWriteParams struct {
	Todos []Todo `json:"todos"`
}

func (t TodoWriteParams) IsPresent() bool {
	return true
}
