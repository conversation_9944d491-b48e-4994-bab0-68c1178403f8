package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var GrepSpec = tools.Spec[GrepParams, tools.NotPresent, tools.NotPresent]{}

type GrepParams struct {
	Pattern     string `json:"pattern"`
	Path        string `json:"path,omitempty"`
	Glob        string `json:"glob,omitempty"`
	Type        string `json:"type,omitempty"`
	OutputMode  string `json:"output_mode,omitempty"`
	ContextB    *int   `json:"-B,omitempty"`
	ContextA    *int   `json:"-A,omitempty"`
	ContextC    *int   `json:"-C,omitempty"`
	LineNumbers *bool  `json:"-n,omitempty"`
	IgnoreCase  *bool  `json:"-i,omitempty"`
	HeadLimit   *int   `json:"head_limit,omitempty"`
	Multiline   *bool  `json:"multiline,omitempty"`
}

func (g GrepParams) IsPresent() bool {
	return true
}
