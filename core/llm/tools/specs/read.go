package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var ReadSpec = tools.Spec[ReadParams, tools.NotPresent, ReadResponseMetadata]{}

type ReadParams struct {
	FilePath string `json:"file_path"`
	Offset   *int   `json:"offset,omitempty"`
	Limit    *int   `json:"limit,omitempty"`
}

func (r ReadParams) IsPresent() bool {
	return true
}

type ReadResponseMetadata struct {
	FilePath string `json:"file_path"`
	Content  string `json:"content"`
}

func (r ReadResponseMetadata) IsPresent() bool {
	return true
}
