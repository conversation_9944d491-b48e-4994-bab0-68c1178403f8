package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var WriteSpec = tools.Spec[WriteParams, WritePermissionsParams, WriteResponseMetadata]{}

type WriteParams struct {
	FilePath string `json:"file_path"`
	Content  string `json:"content"`
}

func (w WriteParams) IsPresent() bool {
	return true
}

type WritePermissionsParams struct {
	FilePath string `json:"file_path"`
	Diff     string `json:"diff"`
}

func (w WritePermissionsParams) IsPresent() bool {
	return true
}

type WriteResponseMetadata struct {
	Type        string `json:"type"` // "create" or "update"
	FilePath    string `json:"file_path"`
	Diff        string `json:"diff"`
	Additions   int    `json:"additions"`
	Removals    int    `json:"removals"`
	Encoding    string `json:"encoding"`
	LineEnding  string `json:"line_ending"`
	AtomicWrite bool   `json:"atomic_write"`
}

func (w WriteResponseMetadata) IsPresent() bool {
	return true
}
