package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var ExitPlanModeSpec = tools.Spec[ExitPlanModeParams, ExitPlanModePermissionParams, tools.NotPresent]{}

type ExitPlanModeParams struct {
	Plan string `json:"plan"`
}

func (e ExitPlanModeParams) IsPresent() bool {
	return true
}

type ExitPlanModePermissionParams struct {
	Plan string `json:"plan"`
}

func (e ExitPlanModePermissionParams) IsPresent() bool {
	return true
}
