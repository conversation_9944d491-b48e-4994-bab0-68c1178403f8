package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var PatchSpec = tools.Spec[PatchParams, EditPermissionsParams, PatchResponseMetadata]{}

type PatchParams struct {
	PatchText string `json:"patch_text"`
}

func (p PatchParams) IsPresent() bool {
	return true
}

type PatchResponseMetadata struct {
	FilesChanged []string `json:"files_changed"`
	Additions    int      `json:"additions"`
	Removals     int      `json:"removals"`
}

func (p PatchResponseMetadata) IsPresent() bool {
	return true
}
