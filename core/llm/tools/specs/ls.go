package specs

import "github.com/qoder-ai/qoder-cli/core/llm/tools"

var LsSpec = tools.Spec[LSParams, tools.NotPresent, LSResponseMetadata]{}

type LSParams struct {
	Path   string   `json:"path"`
	Ignore []string `json:"ignore,omitempty"`
}

func (l LSParams) IsPresent() bool {
	return true
}

type LSResponseMetadata struct {
	NumberOfFiles int  `json:"number_of_files"`
	Truncated     bool `json:"truncated"`
}

func (l LSResponseMetadata) IsPresent() bool {
	return true
}
