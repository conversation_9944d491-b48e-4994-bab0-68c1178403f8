package models

const (
	ProviderQoder        ModelProvider = "qoder"
	QoderSonnet4Thinking ModelId       = "idealab_claude_sonnet4_thinking"
	QoderSonnet4                       = "idealab_claude_sonnet4"
)

var QoderModels = map[ModelId]Model{
	QoderSonnet4Thinking: {
		Id:                  QoderSonnet4Thinking,
		Name:                "Qoder Sonnet 4 Thinking",
		Provider:            ProviderQoder,
		APIModel:            "idealab_claude_sonnet4_thinking",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       200000,
		DefaultMaxTokens:    50000,
		CanReason:           true,
		SupportsAttachments: true,
	},
	QoderSonnet4: {
		Id:                  QoderSonnet4,
		Name:                "Qoder Sonnet 4",
		Provider:            ProviderQoder,
		APIModel:            "idealab_claude_sonnet4",
		CostPer1MIn:         2.00,
		CostPer1MInCached:   0.50,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        8.00,
		ContextWindow:       200000,
		DefaultMaxTokens:    50000,
		SupportsAttachments: true,
	},
}
