package agent

import (
	"context"
	"errors"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
)

var (
	ErrRequestCancelled = errors.New("request cancelled by user")
	ErrSessionBusy      = errors.New("session is currently processing another request")
)

type EnvType string

const (
	AgentEventTypeError     EnvType = "error"
	AgentEventTypeSystem    EnvType = "system"
	AgentEventTypeUser      EnvType = "user"
	AgentEventTypeAssistant EnvType = "assistant"
	AgentEventTypeResult    EnvType = "result"
	AgentEventTypeSummarize EnvType = "summarize"
)

type Config struct {
	Cwd            string         `json:"cwd,omitempty"`
	Tools          []string       `json:"tools,omitempty"`
	McpServers     []string       `json:"mcp_servers,omitempty"`
	Model          models.ModelId `json:"model,omitempty"`
	PermissionMode string         `json:"permission_mode,omitempty"`
	ApiKeySource   string         `json:"api_key_source,omitempty"`
}

type Event struct {
	*Config   `json:",omitempty"`
	Type      EnvType          `json:"type"`
	Subtype   string           `json:"subtype,omitempty"`
	Message   *message.Message `json:"message,omitempty"`
	Error     error            `json:"error,omitempty"`
	SessionID string           `json:"session_id"`
	Progress  string           `json:"progress,omitempty"`
	Done      bool             `json:"done"`
}

type Agent interface {
	pubsub.Subscriber[Event]
	Model() models.Model
	Run(ctx context.Context, sessionID string, content string, attachments ...message.Attachment) (<-chan Event, error)
	Cancel(sessionID string)
	IsSessionBusy(sessionID string) bool
	IsBusy() bool
	Update(agentName config.AgentName, modelID models.ModelId) error
	Summarize(ctx context.Context, sessionID string) error
}
