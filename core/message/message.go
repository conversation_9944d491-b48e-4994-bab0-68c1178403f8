package message

import (
	"context"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
)

type CreateMessageParams struct {
	Role  Role
	Parts []ContentPart
	Model models.ModelId
}

func (c CreateMessageParams) FinishedPart() *Finish {
	for _, part := range c.Parts {
		if c, ok := part.(Finish); ok {
			return &c
		}
	}
	return nil
}

type MessageService interface {
	pubsub.Subscriber[Message]
	Create(ctx context.Context, sessionID string, params CreateMessageParams) (Message, error)
	Update(ctx context.Context, message Message) error
	List(ctx context.Context, sessionID string) ([]Message, error)
}
