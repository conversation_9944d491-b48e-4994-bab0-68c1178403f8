package config

import (
	"github.com/qoder-ai/qoder-cli/core/llm/models"
)

type McpType string

const (
	McpStdio McpType = "stdio"
	McpSse   McpType = "sse"
)

type AgentName string

const (
	AgentCoder      AgentName = "coder"
	AgentReviewer   AgentName = "reviewer"
	AgentSummarizer AgentName = "summarizer"
	AgentTask       AgentName = "task"
	AgentTitle      AgentName = "title"
)

type AgentConfig struct {
	Model           models.ModelId `json:"model"`
	MaxTokens       int64          `json:"maxTokens"`
	ReasoningEffort string         `json:"reasoningEffort"`
}

type ShellConfig struct {
	Path string   `json:"path,omitempty"`
	Args []string `json:"args,omitempty"`
}

type McpServer struct {
	Command string            `json:"command"`
	Env     []string          `json:"env"`
	Args    []string          `json:"args"`
	Type    McpType           `json:"type"`
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers"`
}

type ProviderConfig struct {
	ApiKey   string `json:"apiKey"`
	Disabled bool   `json:"disabled"`
}

type LspConfig struct {
	Enabled bool     `json:"enabled"`
	Command string   `json:"command"`
	Args    []string `json:"args"`
	Options any      `json:"options"`
}

type Config struct {
	WorkingDir         string                                        `json:"workingDir"`
	ContextPaths       []string                                      `json:"contextPaths,omitempty"`
	AutoCompact        bool                                          `json:"autoCompact,omitempty"`
	Shell              ShellConfig                                   `json:"shell,omitempty"`
	McpServers         map[string]McpServer                          `json:"mcpServers,omitempty"`
	Providers          map[models.ModelProvider]ProviderConfig       `json:"providers,omitempty"`
	LspConfigs         map[string]LspConfig                          `json:"lspConfigs,omitempty"`
	Agents             map[AgentName]AgentConfig                     `json:"agents,omitempty"`
	Debug              bool                                          `json:"debug,omitempty"`
	DebugLsp           bool                                          `json:"debugLsp,omitempty"`
	CustomSystemPrompt *string                                       `json:"customSystemPrompt,omitempty"`
	ModelUpdateHook    func(name AgentName, id models.ModelId) error `json:"-"`
}

func (c *Config) GetAgent(name AgentName) (AgentConfig, bool) {
	agent, exists := c.Agents[name]
	return agent, exists
}

func (c *Config) GetProvider(provider models.ModelProvider) (ProviderConfig, bool) {
	providerConfig, exists := c.Providers[provider]
	return providerConfig, exists
}

func (c *Config) TriggerModelUpdate(agentName AgentName, modelId models.ModelId) error {
	return c.ModelUpdateHook(agentName, modelId)
}
