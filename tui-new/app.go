package tui_new

import (
	"context"
	"fmt"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/llm/agent"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"github.com/qoder-ai/qoder-cli/tui-new/components/chat"
	"github.com/qoder-ai/qoder-cli/tui-new/components/dialog"
	"github.com/qoder-ai/qoder-cli/tui-new/config"
	tuiconfig "github.com/qoder-ai/qoder-cli/tui-new/config"
	"github.com/qoder-ai/qoder-cli/tui-new/util"
)

// appModel 表示主应用程序状态
type appModel struct {
	app             app.App
	permOper        core.PermissionOperator
	selectedSession core.Session
	tuiConfig       *tuiconfig.TuiConfig

	// UI 组件
	messages    *chat.MessagesComponent
	editor      *chat.EditorComponent
	interaction *chat.InteractionComponent
	status      *chat.StatusComponent
	hint        *chat.HintComponent

	// 状态管理
	currentMode  config.AppMode
	currentFocus config.FocusComponent
	width        int
	height       int
	initialized  bool

	// 窗口调整防抖
	resizeDebouncing bool // 是否正在防抖等待中
	pendingWidth     int  // 待处理的宽度
	pendingHeight    int  // 待处理的高度

	// 对话框状态
	showQuit bool
	quit     dialog.QuitDialog
}

// Init 初始化应用程序和所有组件
func (a appModel) Init() tea.Cmd {
	var cmds []tea.Cmd

	cmd := a.status.Init()
	cmds = append(cmds, cmd)
	cmd = a.messages.Init()
	cmds = append(cmds, cmd)
	cmd = a.editor.Init()
	cmds = append(cmds, cmd)
	cmd = a.interaction.Init()
	cmds = append(cmds, cmd)
	cmd = a.hint.Init()
	cmds = append(cmds, cmd)
	cmd = a.quit.Init()
	cmds = append(cmds, cmd)

	return tea.Batch(cmds...)
}

func (a *appModel) UpdateAllComponents(msg tea.Msg) []tea.Cmd {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		// 消息组件需要实际高度进行动态视口计算
		messagesMsg := tea.WindowSizeMsg{Width: msg.Width, Height: msg.Height}
		othersMsg := tea.WindowSizeMsg{Width: msg.Width, Height: 0}

		m, cmd := a.messages.Update(messagesMsg)
		a.messages = m.(*chat.MessagesComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		e, cmd := a.editor.Update(othersMsg)
		a.editor = e.(*chat.EditorComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		i, cmd := a.interaction.Update(othersMsg)
		a.interaction = i.(*chat.InteractionComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		s, cmd := a.status.Update(othersMsg)
		a.status = s.(*chat.StatusComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		h, cmd := a.hint.Update(othersMsg)
		a.hint = h.(*chat.HintComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

	default:
		// 将其他消息传递给所有组件
		m, cmd := a.messages.Update(msg)
		a.messages = m.(*chat.MessagesComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		e, cmd := a.editor.Update(msg)
		a.editor = e.(*chat.EditorComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		i, cmd := a.interaction.Update(msg)
		a.interaction = i.(*chat.InteractionComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		s, cmd := a.status.Update(msg)
		a.status = s.(*chat.StatusComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

		h, cmd := a.hint.Update(msg)
		a.hint = h.(*chat.HintComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	return cmds
}

// reinitializeAllComponents 重新初始化所有组件以确保完全重新渲染
func (a *appModel) reinitializeAllComponents() []tea.Cmd {
	var cmds []tea.Cmd

	// 重新初始化所有UI组件
	cmd := a.status.Init()
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	cmd = a.messages.Init()
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	cmd = a.editor.Init()
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	cmd = a.interaction.Init()
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	cmd = a.hint.Init()
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	// 重新初始化对话框
	cmd = a.quit.Init()
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	return cmds
}

// Update 处理应用程序状态更新
func (a appModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		a.width, a.height = msg.Width, msg.Height

		// 如果是初始化阶段，直接处理不需要防抖
		if !a.initialized {
			// 更新所有组件尺寸
			cmds = append(cmds, a.UpdateAllComponents(msg)...)

			// 发送布局更新消息，确保消息组件获得所有组件的正确高度
			_, hintHeight := a.hint.GetSize()
			_, interactionHeight := a.interaction.GetSize()
			layoutMsg := config.LayoutUpdateMsg{
				StatusHeight:      config.StatusHeight,
				EditorHeight:      config.EditorHeight,
				HintHeight:        hintHeight,
				InteractionHeight: interactionHeight,
				IsInteractionMode: a.currentMode == config.InteractionMode,
			}
			m, cmd := a.messages.Update(layoutMsg)
			a.messages = m.(*chat.MessagesComponent)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}

			// 标记已初始化
			a.initialized = true
			return a, tea.Batch(cmds...)
		}

		// 已初始化的情况：启动防抖机制
		a.pendingWidth = msg.Width
		a.pendingHeight = msg.Height
		a.resizeDebouncing = true

		// 启动200ms防抖定时器
		cmds = append(cmds, util.CreateResizeDebounceTimer(msg.Width, msg.Height, 200*time.Millisecond))
		return a, tea.Batch(cmds...)

	case util.WindowResizeDebounceMsg:
		// 防抖定时器到期，检查是否仍是最新的尺寸
		if a.resizeDebouncing && msg.Width == a.pendingWidth && msg.Height == a.pendingHeight {
			// 尺寸未再变化，执行真正的重置和重绘
			a.resizeDebouncing = false

			// 窗口尺寸改变时彻底重置终端内容，包括清理缓冲区
			cmds = append(cmds, util.ExecuteTerminalFullReset())

			// 重新初始化所有组件状态以确保完全重新渲染
			cmds = append(cmds, a.reinitializeAllComponents()...)

			// 更新所有组件尺寸
			windowSizeMsg := tea.WindowSizeMsg{Width: msg.Width, Height: msg.Height}
			cmds = append(cmds, a.UpdateAllComponents(windowSizeMsg)...)

			// 发送布局更新消息，确保消息组件获得所有组件的正确高度
			_, hintHeight := a.hint.GetSize()
			_, interactionHeight := a.interaction.GetSize()
			layoutMsg := config.LayoutUpdateMsg{
				StatusHeight:      config.StatusHeight,
				EditorHeight:      config.EditorHeight,
				HintHeight:        hintHeight,
				InteractionHeight: interactionHeight,
				IsInteractionMode: a.currentMode == config.InteractionMode,
			}
			m, cmd := a.messages.Update(layoutMsg)
			a.messages = m.(*chat.MessagesComponent)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}
		return a, tea.Batch(cmds...)

	case tea.KeyMsg:
		// 根据当前模式路由键盘输入
		switch a.currentMode {
		case config.InteractionMode:
			if a.interaction.IsActive() {
				m, cmd := a.interaction.Update(msg)
				a.interaction = m.(*chat.InteractionComponent)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
				return a, tea.Batch(cmds...)
			}
			// 如果交互不活跃，切换回普通模式
			a.currentMode = config.NormalMode
			fallthrough
		default:
			// 处理全局键和基于焦点的路由
			switch msg.String() {
			case "ctrl+c":
				return a, tea.Quit
			case "ctrl+q":
				a.showQuit = true
				return a, nil
			case "tab":
				// 在编辑器和消息之间切换焦点
				if a.currentFocus == config.FocusEditor {
					// 只有当消息组件需要滚动时才允许切换到消息组件
					if a.messages.NeedsScrolling() {
						a.currentFocus = config.FocusMessages
						a.messages.SetFocus(true)
						a.editor.SetFocus(false)
						return a, tea.EnableMouseCellMotion
					}
					// 如果不需要滚动，Tab键无效，保持在编辑器焦点
					return a, nil
				} else {
					a.currentFocus = config.FocusEditor
					a.messages.SetFocus(false)
					a.editor.SetFocus(true)
					return a, tea.DisableMouse
				}
			default:
				// 根据当前焦点路由键盘事件
				switch a.currentFocus {
				case config.FocusMessages:
					m, cmd := a.messages.Update(msg)
					a.messages = m.(*chat.MessagesComponent)
					if cmd != nil {
						cmds = append(cmds, cmd)
					}
					return a, tea.Batch(cmds...)
				default: // FocusEditor
					m, cmd := a.editor.Update(msg)
					a.editor = m.(*chat.EditorComponent)
					if cmd != nil {
						cmds = append(cmds, cmd)
					}
					return a, tea.Batch(cmds...)
				}
			}
		}

	case pubsub.Event[core.PermissionRequest]:
		a.currentMode = config.InteractionMode
		a.interaction.SetPermissionRequest(msg.Payload)

		// 模式切换后发送布局更新消息
		_, hintHeight := a.hint.GetSize()
		_, interactionHeight := a.interaction.GetSize()
		layoutMsg := config.LayoutUpdateMsg{
			StatusHeight:      config.StatusHeight,
			EditorHeight:      config.EditorHeight,
			HintHeight:        hintHeight,
			InteractionHeight: interactionHeight,
			IsInteractionMode: true,
		}
		m, cmd := a.messages.Update(layoutMsg)
		a.messages = m.(*chat.MessagesComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)

	case chat.InteractionCompleteMsg:
		a.currentMode = config.NormalMode
		a.interaction.Clear()

		// 处理权限响应
		if msg.Request != nil {
			switch msg.Action {
			case dialog.PermissionAllow:
				a.permOper.Grant(*msg.Request)
			case dialog.PermissionAllowForSession:
				a.permOper.GrantPersistent(*msg.Request)
			case dialog.PermissionDeny:
				a.permOper.Deny(*msg.Request)
			}
		}

		// 模式切换后发送布局更新消息
		_, hintHeight := a.hint.GetSize()
		_, interactionHeight := a.interaction.GetSize()
		layoutMsg := config.LayoutUpdateMsg{
			StatusHeight:      config.StatusHeight,
			EditorHeight:      config.EditorHeight,
			HintHeight:        hintHeight,
			InteractionHeight: interactionHeight,
			IsInteractionMode: false,
		}
		m, cmd := a.messages.Update(layoutMsg)
		a.messages = m.(*chat.MessagesComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)

	case pubsub.Event[core.Session]:
		// 会话更新，更新选定的会话和消息
		if msg.Type == pubsub.UpdatedEvent {
			a.selectedSession = msg.Payload
			m, cmd := a.messages.Update(chat.SessionChangedMsg{Session: msg.Payload})
			a.messages = m.(*chat.MessagesComponent)
			cmds = append(cmds, cmd)
		}

	case pubsub.Event[agent.Event]:
		payload := msg.Payload

		// 处理错误
		if payload.Error != nil {
			s, cmd := a.status.Update(util.InfoMsg{
				Type: util.InfoTypeError,
				Msg:  payload.Error.Error(),
				TTL:  5 * time.Second,
			})
			a.status = s.(*chat.StatusComponent)
			cmds = append(cmds, cmd)
			return a, tea.Batch(cmds...)
		}

		// 处理AI回复完成
		if payload.Done && payload.Type == agent.AgentEventTypeResult && a.selectedSession.Id != "" {
			// 重新加载消息，这会触发MessagesComponent更新
			m, cmd := a.messages.Update(pubsub.Event[core.Session]{
				Type:    pubsub.UpdatedEvent,
				Payload: a.selectedSession,
			})
			a.messages = m.(*chat.MessagesComponent)
			cmds = append(cmds, cmd)
		}

		return a, tea.Batch(cmds...)

	case util.ClearStatusMsg:
		s, _ := a.status.Update(msg)
		a.status = s.(*chat.StatusComponent)

	// 🔧 修复：将SendMsg等事件处理移到正确位置
	case chat.SendMsg:
		// 处理消息发送
		return a, a.sendMessage(msg.Text, msg.Attachments)

	case dialog.CloseQuitMsg:
		a.showQuit = false
		return a, nil

	case util.InfoMsg:
		s, cmd := a.status.Update(msg)
		a.status = s.(*chat.StatusComponent)
		cmds = append(cmds, cmd)
		return a, tea.Batch(cmds...)

	case chat.HintHeightChangeMsg:
		// Hint组件高度变化，协调所有组件并发送统一的布局更新消息
		_, hintHeight := a.hint.GetSize()
		_, interactionHeight := a.interaction.GetSize()
		layoutMsg := config.LayoutUpdateMsg{
			StatusHeight:      config.StatusHeight,
			EditorHeight:      config.EditorHeight,
			HintHeight:        hintHeight,
			InteractionHeight: interactionHeight,
			IsInteractionMode: a.currentMode == config.InteractionMode,
		}
		m, cmd := a.messages.Update(layoutMsg)
		a.messages = m.(*chat.MessagesComponent)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		return a, tea.Batch(cmds...)

	case pubsub.Event[logging.LogMessage]:
		if msg.Payload.Persist {
			switch msg.Payload.Level {
			case "error":
				s, cmd := a.status.Update(util.InfoMsg{
					Type: util.InfoTypeError,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.status = s.(*chat.StatusComponent)
				cmds = append(cmds, cmd)
			case "info":
				s, cmd := a.status.Update(util.InfoMsg{
					Type: util.InfoTypeInfo,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.status = s.(*chat.StatusComponent)
				cmds = append(cmds, cmd)
			case "warn":
				s, cmd := a.status.Update(util.InfoMsg{
					Type: util.InfoTypeWarn,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.status = s.(*chat.StatusComponent)
				cmds = append(cmds, cmd)
			default:
				s, cmd := a.status.Update(util.InfoMsg{
					Type: util.InfoTypeInfo,
					Msg:  msg.Payload.Message,
					TTL:  msg.Payload.PersistTime,
				})
				a.status = s.(*chat.StatusComponent)
				cmds = append(cmds, cmd)
			}
		}
		return a, tea.Batch(cmds...)

	default:
		// 处理鼠标事件，只有当消息组件聚焦时才处理
		if mouseMsg, isMouseMsg := msg.(tea.MouseMsg); isMouseMsg {
			// 只有当消息组件聚焦时才将鼠标事件传递给它
			if a.currentFocus == config.FocusMessages {
				m, cmd := a.messages.Update(mouseMsg)
				a.messages = m.(*chat.MessagesComponent)
				cmds = append(cmds, cmd)
			}
		} else if _, isKeyMsg := msg.(tea.KeyMsg); !isKeyMsg {
			// 对于其他非键盘事件，传递给所有组件
			return a, tea.Batch(append(cmds, a.UpdateAllComponents(msg)...)...)
		}
	}

	// 处理退出对话框
	if a.showQuit {
		q, quitCmd := a.quit.Update(msg)
		a.quit = q.(dialog.QuitDialog)
		cmds = append(cmds, quitCmd)
		// 只阻止键盘消息，发送所有其他消息
		if _, ok := msg.(tea.KeyMsg); ok {
			return a, tea.Batch(cmds...)
		}
	}

	return a, tea.Batch(cmds...)
}

// sendMessage 发送消息给AI Agent
func (a *appModel) sendMessage(text string, attachments []message.Attachment) tea.Cmd {
	// 如果没有活跃的session，创建一个新的
	if a.selectedSession.Id == "" {
		newSession, err := a.app.GetSessionService().CreateWithConfig(context.Background(), "New Session", &core.SessionConfig{})
		if err != nil {
			return func() tea.Msg {
				return util.InfoMsg{
					Type: util.InfoTypeError,
					Msg:  "创建会话失败: " + err.Error(),
					TTL:  5 * time.Second,
				}
			}
		}
		a.selectedSession = newSession

		// 通知MessagesComponent新的session，但不要返回，继续发送消息
		sessionCmd := func() tea.Msg {
			return chat.SessionChangedMsg{Session: newSession}
		}

		// 继续执行消息发送逻辑
		sendCmd := func() tea.Msg {
			_, err := a.app.GetCoderAgent().Run(context.Background(), newSession.Id, text, attachments...)
			if err != nil {
				return util.InfoMsg{
					Type: util.InfoTypeError,
					Msg:  "发送消息失败: " + err.Error(),
					TTL:  5 * time.Second,
				}
			}
			return nil
		}

		// 批量执行：先通知session变化，再发送消息
		return tea.Batch(sessionCmd, sendCmd)
	}

	// 发送消息给AI Agent
	return func() tea.Msg {
		_, err := a.app.GetCoderAgent().Run(context.Background(), a.selectedSession.Id, text, attachments...)
		if err != nil {
			return util.InfoMsg{
				Type: util.InfoTypeError,
				Msg:  "发送消息失败: " + err.Error(),
				TTL:  5 * time.Second,
			}
		}
		return nil
	}
}

// View 渲染应用程序界面
func (a appModel) View() string {
	// 计算最小窗口高度要求
	welcomeHeight := a.messages.GetWelcomeHeight()
	minViewportHeight := welcomeHeight + 2
	minRequiredHeight := minViewportHeight + config.EditorHeight + config.StatusHeight

	// 检查最小窗口尺寸
	if a.height < minRequiredHeight || a.width < config.MinWindowWidth {
		return fmt.Sprintf(config.DefaultUITexts.WindowTooSmallMessage,
			config.MinWindowWidth, minRequiredHeight, a.width, a.height)
	}

	// 根据当前模式渲染底部组件和布局
	switch a.currentMode {
	case config.InteractionMode:
		// 交互模式：状态栏 + 消息视口 + 交互框（无编辑器和hint）
		interactionView := a.interaction.View()
		return a.status.View() + "\n" + a.messages.View() + "\n" + interactionView
	default:
		// 正常模式：状态栏 + 消息视口 + 编辑器 + 快捷键提示
		editorView := a.editor.View()
		hintView := a.hint.View()
		return a.status.View() + "\n" + a.messages.View() + "\n\n" + editorView + "\n" + hintView
	}
}

// New 创建新的应用程序实例
func New(app app.App, permOper core.PermissionOperator, tuiConfig *tuiconfig.TuiConfig) tea.Model {
	return &appModel{
		app:             app,
		permOper:        permOper,
		tuiConfig:       tuiConfig,
		selectedSession: core.Session{},

		// 初始化UI组件
		messages:    chat.NewMessagesComponent(app),
		editor:      chat.NewEditorComponent(app),
		interaction: chat.NewInteractionComponent(),
		status:      chat.NewStatusCmp(app),
		hint:        chat.NewHintComponent(),

		// 设置默认模式和焦点
		currentMode:  config.NormalMode,
		currentFocus: config.FocusEditor,

		// 对话框状态
		showQuit: false,
		quit:     dialog.NewQuitCmp(),
	}
}
