package chat

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/x/ansi"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/llm/tools"
	"github.com/qoder-ai/qoder-cli/core/llm/tools/specs"
	"github.com/qoder-ai/qoder-cli/core/message"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"github.com/qoder-ai/qoder-cli/tui-new/config"
	"github.com/qoder-ai/qoder-cli/tui-new/diff"
	"github.com/qoder-ai/qoder-cli/tui-new/styles"
	"github.com/qoder-ai/qoder-cli/tui-new/theme"
)

// MessagesComponent 处理基于视口滚动的消息显示
type MessagesComponent struct {
	width        int
	height       int
	windowHeight int
	app          app.App
	session      core.Session
	messages     []message.Message
	spinner      spinner.Model

	// 视口相关字段
	viewport viewport.Model
	ready    bool
	focused  bool

	// 其他组件高度（通过LayoutUpdateMsg更新）
	statusHeight      int
	editorHeight      int
	hintHeight        int
	interactionHeight int
	isInteractionMode bool
}

// SessionChangedMsg 会话更改时发送
type SessionChangedMsg struct {
	Session core.Session
}

// messagesLoadedMsg 内部使用，当消息加载完成时
type messagesLoadedMsg struct {
	messages []message.Message
}

// NewMessagesComponent 创建新的消息组件
func NewMessagesComponent(app app.App) *MessagesComponent {
	s := spinner.New()
	s.Spinner = spinner.Dot
	s.Style = styles.BaseStyle().Foreground(theme.CurrentTheme().Primary())

	return &MessagesComponent{
		app:               app,
		messages:          []message.Message{},
		spinner:           s,
		ready:             false,
		statusHeight:      config.StatusHeight, // 从config获取默认高度
		editorHeight:      config.EditorHeight,
		hintHeight:        1, // 初始hint高度为1行
		interactionHeight: 0, // 初始交互高度为0
		isInteractionMode: false,
	}
}

// Init 初始化组件
func (m *MessagesComponent) Init() tea.Cmd {
	return m.spinner.Tick
}

// Update 处理组件状态更新
func (m *MessagesComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	// 更新旋转器
	spinnerModel, spinnerCmd := m.spinner.Update(msg)
	m.spinner = spinnerModel
	cmds = append(cmds, spinnerCmd)

	// 当旋转器更新时刷新内容
	if spinnerCmd != nil && m.ready {
		content := m.renderAllMessages()
		m.viewport.SetContent(content)
	}

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.windowHeight = msg.Height

		// 重新计算视口高度
		m.recalculateViewportHeight()

		if !m.ready {
			// 初始化动态尺寸的视口
			m.viewport = viewport.New(m.width, m.viewport.Height)
			m.viewport.Style = lipgloss.NewStyle()
			m.ready = true

			// 设置初始内容
			content := m.renderAllMessages()
			m.viewport.SetContent(content)
			m.viewport.GotoBottom()
		} else {
			// 窗口变化时更新宽度
			m.viewport.Width = m.width
			// 重新渲染内容
			content := m.renderAllMessages()
			m.viewport.SetContent(content)
			m.viewport.GotoBottom()
		}
		return m, tea.Batch(cmds...)

	case SessionChangedMsg:
		m.session = msg.Session
		// 为新会话加载消息
		loadCmd := m.loadMessages()
		cmds = append(cmds, loadCmd)
		return m, tea.Batch(cmds...)

	case pubsub.Event[core.Session]:
		// 会话更新，如果是当前会话则重新加载消息
		if msg.Type == pubsub.UpdatedEvent && msg.Payload.Id == m.session.Id {
			m.session = msg.Payload
			loadCmd := m.loadMessages()
			cmds = append(cmds, loadCmd)
		}

	case pubsub.Event[message.Message]:
		// 实时消息更新（用于流式显示）
		if msg.Payload.SessionId == m.session.Id {
			updateCmd := m.handleMessageEvent(msg)
			if updateCmd != nil {
				cmds = append(cmds, updateCmd)
			}
		}

	case messagesLoadedMsg:
		m.messages = msg.messages
		// 更新视口内容
		if m.ready {
			content := m.renderAllMessages()
			m.viewport.SetContent(content)
			m.viewport.GotoBottom() // 滚动到最新内容
		}
		return m, tea.Batch(cmds...)

	case config.LayoutUpdateMsg:
		// 布局更新，更新所有组件高度并重新计算viewport高度
		m.statusHeight = msg.StatusHeight
		m.editorHeight = msg.EditorHeight
		m.hintHeight = msg.HintHeight
		m.interactionHeight = msg.InteractionHeight
		m.isInteractionMode = msg.IsInteractionMode
		if m.ready {
			m.recalculateViewportHeight()
			content := m.renderAllMessages()
			m.viewport.SetContent(content)
			m.viewport.GotoBottom()
		}
		return m, nil

	case tea.MouseMsg:
		// 只有在组件有焦点且准备就绪时才处理鼠标事件
		if m.focused && m.ready {
			switch msg.Action {
			case tea.MouseActionPress:
				switch msg.Button {
				case tea.MouseButtonWheelUp:
					m.viewport.ScrollUp(1)
					return m, nil
				case tea.MouseButtonWheelDown:
					m.viewport.ScrollDown(1)
					return m, nil
				default:
					// 让 viewport 处理其他鼠标按下事件
					var cmd tea.Cmd
					m.viewport, cmd = m.viewport.Update(msg)
					cmds = append(cmds, cmd)
				}
			default:
				// 让 viewport 处理其他鼠标事件（释放、移动等）
				var cmd tea.Cmd
				m.viewport, cmd = m.viewport.Update(msg)
				cmds = append(cmds, cmd)
			}
		}
		return m, tea.Batch(cmds...)
	}

	// 处理视口的滚动事件
	if m.ready {
		var cmd tea.Cmd
		m.viewport, cmd = m.viewport.Update(msg)
		cmds = append(cmds, cmd)
	}

	return m, tea.Batch(cmds...)
}

func (m *MessagesComponent) loadMessages() tea.Cmd {
	if m.session.Id == "" {
		m.messages = []message.Message{}
		return nil
	}

	return tea.Batch(func() tea.Msg {
		messages, err := m.app.GetMessageService().List(context.Background(), m.session.Id)
		if err != nil {
			return nil // 静默处理错误，避免干扰用户体验
		}
		return messagesLoadedMsg{messages: messages}
	})
}

func (m *MessagesComponent) View() string {
	// 统一使用 viewport 渲染，移除 ready 状态的特殊处理
	content := m.viewport.View()

	// 获取帮助文本（只在需要滚动时显示，焦点状态通过help分割线高亮指示）
	helpText := m.helpView()

	// 如果有帮助文本，将其放在内容上方
	if helpText != "" {
		content = helpText + "\n" + content
	}

	// 焦点状态通过help分割线的高亮来指示
	normalStyle := lipgloss.NewStyle().PaddingLeft(2)
	content = normalStyle.Render(content)

	return content
}

func (m *MessagesComponent) helpView() string {
	// 只有当viewport需要滚动时才显示帮助文本
	if !m.ready || !m.needsScrolling() {
		return ""
	}

	// 根据焦点状态选择样式
	var helpStyle, separatorStyle lipgloss.Style
	if m.focused {
		// 焦点状态：高亮显示
		helpStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("36"))
		separatorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("36")) // 分割线也使用青色
	} else {
		// 非焦点状态：正常显示
		helpStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("241"))
		separatorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("240"))
	}

	// 添加icon和帮助文本
	var helpText string
	if m.focused {
		helpText = styles.QoderCliIcon + " ↑/↓/MouseWheel: Scroll  Tab: Blur"
	} else {
		helpText = styles.QoderCliIcon + " Tab: Focus messages to scroll"
	}

	// 计算分割线长度，填充剩余空间
	separatorWidth := max(0, m.viewport.Width-lipgloss.Width(helpText)-2) // 预留2字符空间
	separator := strings.Repeat("─", separatorWidth)

	// 将文字和分割线放在同一行
	return helpStyle.Render(helpText) + " " + separatorStyle.Render(separator)
}

// needsScrolling 检查viewport是否需要滚动（私有方法）
func (m *MessagesComponent) needsScrolling() bool {
	if !m.ready {
		return false
	}

	// 检查总行数是否大于可见行数
	totalLines := m.viewport.TotalLineCount()
	visibleLines := m.viewport.VisibleLineCount()

	return totalLines > visibleLines
}

// NeedsScrolling 公开的滚动检测方法，供外部调用
func (m *MessagesComponent) NeedsScrolling() bool {
	return m.needsScrolling()
}

// SetFocus 设置焦点状态
func (m *MessagesComponent) SetFocus(focused bool) {
	m.focused = focused
}

// GetWelcomeHeight 计算欢迎内容的实际高度
func (m *MessagesComponent) GetWelcomeHeight() int {
	welcomeContent := m.renderWelcomeHeader()
	return lipgloss.Height(welcomeContent)
}

// GetCurrentContentHeight 计算当前所有内容的高度
func (m *MessagesComponent) GetCurrentContentHeight() int {
	if len(m.messages) == 0 {
		return m.GetWelcomeHeight()
	}

	allContent := m.renderAllMessages()
	return lipgloss.Height(allContent)
}

// recalculateViewportHeight 重新计算并更新viewport高度
func (m *MessagesComponent) recalculateViewportHeight() {
	if !m.ready || m.windowHeight == 0 {
		return
	}

	// 使用真实窗口高度计算可用空间，根据当前模式计算组件高度
	var usedHeight int
	if m.isInteractionMode {
		// 交互模式：状态栏 + 交互框
		usedHeight = m.statusHeight + m.interactionHeight
	} else {
		// 正常模式：状态栏 + 编辑框 + 快捷键提示框
		usedHeight = m.statusHeight + m.editorHeight + m.hintHeight
	}
	availableHeight := m.windowHeight - usedHeight

	// 计算最小视口高度（显示完整欢迎消息）
	minViewportHeight := m.GetWelcomeHeight() + 2

	// 计算当前内容高度
	contentHeight := m.GetCurrentContentHeight()

	// 动态视口高度计算：在最小和最大可用高度之间
	var newViewportHeight int
	if contentHeight <= minViewportHeight {
		newViewportHeight = minViewportHeight
	} else if contentHeight >= availableHeight {
		newViewportHeight = availableHeight
	} else {
		newViewportHeight = contentHeight
	}

	// 确保最小高度约束
	if newViewportHeight < minViewportHeight {
		newViewportHeight = minViewportHeight
	}
	if newViewportHeight < config.MinViewportHeight {
		newViewportHeight = config.MinViewportHeight
	}

	// 只有当高度实际改变时才更新
	if m.viewport.Height != newViewportHeight {
		m.viewport.Height = newViewportHeight
	}
}

// renderAllMessages 将所有消息渲染为单个字符串
func (m *MessagesComponent) renderAllMessages() string {
	// 渲染连续对话流
	return m.renderContinuousConversation()
}

// Short ASCII logo for narrow terminals (Gemini-style)
func (m *MessagesComponent) getShortLogo() string {
	return `
  ███████████    ██████████  ████████████  ███████████  ███████████
 ███░░░░░░░███  ███░░░░░░███░░███░░░░░░███░░░███░░░░░█ ░░███░░░░░███
░███      ░███ ░███     ░███ ░███     ░░███ ░███  █ ░   ░███    ░░███
░███      ░███ ░███     ░███ ░███      ░███ ░██████     ████████████
░███    █ ░███ ░███     ░███ ░███      ░███ ░███░░█    ░░███░░░██░░
░███   ░ ██░██ ░███     ░███ ░███     ███░  ░███ ░   █  ░███░ ░░███
  ███████░████   ██████████  ███████████   ███████████  ████   ░█████
 ░░░░░   ░██░   ░░░░░░░░░░  ░░░░░░░░░░░   ░░░░░░░░░░░  ░░░░    ░░░░░
`
}

// Long ASCII logo for wide terminals (Gemini-style)
func (m *MessagesComponent) getLongLogo() string {
	return `
 ███           ███████████    ██████████  ████████████  ███████████  ███████████
░░░███        ███░░░░░░░███  ███░░░░░░███░░███░░░░░░███░░░███░░░░░█ ░░███░░░░░███
  ░░░███     ░███      ░███ ░███     ░███ ░███     ░░███ ░███  █ ░   ░███    ░░███
    ░░░███   ░███      ░███ ░███     ░███ ░███      ░███ ░██████     ████████████
     ███░    ░███    █ ░███ ░███     ░███ ░███      ░███ ░███░░█    ░░███░░░██░░
   ███░      ░███   ░ ██░██ ░███     ░███ ░███     ███░  ░███ ░   █  ░███░ ░░███
 ███░          ███████░████   ██████████  ███████████   ███████████  ████   ░█████
░░░           ░░░░░   ░██░   ░░░░░░░░░░  ░░░░░░░░░░░   ░░░░░░░░░░░  ░░░░    ░░░░░
`
}

func (m *MessagesComponent) renderLogo() string {

	// Get both logo options
	longLogo := m.getLongLogo()
	shortLogo := m.getShortLogo()

	// Calculate width for long logo
	longWidth := 82

	// Choose appropriate logo based on available width
	// Leave some margin for borders and padding
	availableWidth := m.viewport.Width - 4
	var selectedLogo string
	if availableWidth >= longWidth {
		selectedLogo = longLogo
	} else {
		// For narrower terminals, use short logo
		selectedLogo = shortLogo
	}

	return selectedLogo
}

func (m *MessagesComponent) renderWelcomeMessage() string {
	t := theme.CurrentTheme()
	baseStyle := styles.BaseStyle()

	// Main tips that are always shown
	tipsHeader := baseStyle.
		Foreground(t.Text()).
		Render("Tips for getting started:")

	tip1 := baseStyle.
		Foreground(t.Text()).
		Render("1. Ask questions, edit files, or run commands.")

	tip2 := baseStyle.
		Foreground(t.Text()).
		Render("2. Be specific for the best results.")

	// Build tips dynamically
	var tips []string
	tips = append(tips, tipsHeader)
	tips = append(tips, tip1)
	tips = append(tips, tip2)

	// Add conditional tip about help command (always show as tip 3)
	helpTip := baseStyle.
		Foreground(t.Text()).
		Render("3. Type ") +
		baseStyle.
			Foreground(t.Primary()).
			Bold(true).
			Render("/help") +
		baseStyle.
			Foreground(t.Text()).
			Render(" for more information.")

	tips = append(tips, helpTip)

	// Add spacing and join all tips
	welcomeContent := lipgloss.JoinVertical(lipgloss.Left, tips...)

	return welcomeContent
}

func (m *MessagesComponent) renderWorkingStatus() string {
	if !m.app.GetCoderAgent().IsSessionBusy(m.session.Id) {
		return ""
	}

	t := theme.CurrentTheme()

	// 确定当前状态
	status := "Thinking..."
	if len(m.messages) > 0 {
		lastMessage := m.messages[len(m.messages)-1]
		if !lastMessage.IsFinished() && lastMessage.Content().Text != "" {
			status = "Generating..."
		} else if lastMessage.ReasoningContent().Thinking != "" && lastMessage.Content().Text == "" {
			status = "Thinking..."
		}
	}

	// 渲染工作状态
	workingStyle := styles.BaseStyle().
		Width(m.width - 2).
		BorderLeft(true).
		BorderStyle(lipgloss.ThickBorder()).
		BorderForeground(t.Warning()).
		PaddingLeft(1).
		Bold(true)

	statusText := fmt.Sprintf("%s %s", m.spinner.View(), status)
	return workingStyle.Render(statusText)
}

func (m *MessagesComponent) renderContinuousConversation() string {
	var allContent []string

	// 1. 先添加欢迎界面作为对话的开始
	welcomeHeader := m.renderWelcomeHeader()
	allContent = append(allContent, welcomeHeader)

	// 2. 如果有消息，直接渲染内容
	if len(m.messages) > 0 {
		for i, msg := range m.messages {
			switch msg.Role {
			case "user":
				content := m.renderUserMessageContent(msg, false, m.width)
				allContent = append(allContent, content)
			case "assistant":
				contents := m.renderAssistantMessageContent(msg, i, m.messages, "", false, m.width)
				allContent = append(allContent, contents...)
			case "system":
				content := m.renderSystemMessageContent(msg, false, m.width)
				allContent = append(allContent, content)
			}
		}

		// 添加工作状态（如果AI正在工作）
		if m.app.GetCoderAgent().IsSessionBusy(m.session.Id) {
			workingStatus := m.renderWorkingStatus()
			if workingStatus != "" {
				allContent = append(allContent, workingStatus)
			}
		}
	}

	// 3. 组合所有内容 (欢迎界面 + 消息流)
	content := lipgloss.JoinVertical(lipgloss.Left, allContent...)

	// 4. 应用最终样式
	// viewport会自动处理边框，我们只需要设置合适的内容宽度
	contentWidth := m.width - 4 // 简单预留一些边距
	if contentWidth < 10 {
		contentWidth = 10
	}

	style := styles.BaseStyle().
		Width(contentWidth)

	return style.Render(content)
}

func (m *MessagesComponent) renderWelcomeHeader() string {
	// 简洁的Logo
	logo := m.renderLogo()

	// 欢迎信息
	welcome := m.renderWelcomeMessage()

	// 组合内容，使用左对齐，添加上边距避免顶头显示
	content := lipgloss.JoinVertical(
		lipgloss.Left,
		"",
		logo,
		"",
		welcome,
	)

	return content
}

// SetSession 更新当前会话并重新加载消息
func (m *MessagesComponent) SetSession(session core.Session) tea.Cmd {
	m.session = session
	return m.loadMessages()
}

// handleMessageEvent handles real-time message updates (for streaming display)
func (m *MessagesComponent) handleMessageEvent(event pubsub.Event[message.Message]) tea.Cmd {
	var contentChanged bool

	switch event.Type {
	case pubsub.CreatedEvent:
		// New message creation, add to list if it doesn't exist
		messageExists := false
		for _, existing := range m.messages {
			if existing.Id == event.Payload.Id {
				messageExists = true
				break
			}
		}
		if !messageExists {
			m.messages = append(m.messages, event.Payload)
			contentChanged = true
		}

	case pubsub.UpdatedEvent:
		// Message update (streaming content), replace existing message
		for i, existing := range m.messages {
			if existing.Id == event.Payload.Id {
				m.messages[i] = event.Payload
				contentChanged = true
				break
			}
		}
	}

	// Update viewport content if content changed and viewport is ready
	if contentChanged && m.ready {
		// Re-render content
		content := m.renderAllMessages()
		m.viewport.SetContent(content)

		// Handle scrolling logic
		if event.Type == pubsub.CreatedEvent ||
			(len(m.messages) > 0 && m.messages[len(m.messages)-1].Id == event.Payload.Id) {
			// New message or last message update, scroll to bottom
			m.viewport.GotoBottom()
		}
	}

	return nil
}

// toMarkdown handles markdown rendering of content
func (m *MessagesComponent) toMarkdown(content string, focused bool, width int) string {
	r := styles.GetMarkdownRenderer(width)
	rendered, _ := r.Render(content)
	return rendered
}

// renderMessage handles core message rendering logic for the TUI
func (m *MessagesComponent) renderMessage(thinking string, msg string, isUser bool, isFocused bool, width int, info ...string) string {
	t := theme.CurrentTheme()
	baseStyle := styles.BaseStyle()
	var parts []string

	if thinking != "" {
		thinkingText := "🧠 " + strings.TrimSuffix(thinking, "\n")
		styledThinking := baseStyle.
			Width(width).
			Foreground(t.Accent()).
			Render(thinkingText)
		parts = append(parts, styledThinking)
	}

	var mainParts []string

	if msg != "" {
		style := styles.BaseStyle().
			Width(width - 1).
			BorderLeft(true).
			BorderForeground(t.Primary()).
			BorderStyle(lipgloss.ThickBorder())

		if isUser {
			style = style.BorderForeground(t.Secondary())
		}

		// 应用markdown格式化
		mainParts = append(mainParts, strings.TrimSuffix(m.toMarkdown(msg, isFocused, width), "\n"))

		if len(info) > 0 {
			mainParts = append(mainParts, info...)
		}

		parts = append(parts, style.Render(
			lipgloss.JoinVertical(
				lipgloss.Left,
				mainParts...,
			),
		))
	}

	return styles.BaseStyle().
		Render(lipgloss.JoinVertical(
			lipgloss.Left,
			parts...,
		))
}

// 简化的消息内容渲染方法 - 移除position计算冗余

// renderUserMessageContent 渲染用户消息内容
func (m *MessagesComponent) renderUserMessageContent(msg message.Message, isFocused bool, width int) string {
	// 准备附件信息
	var attachmentInfo []string
	attachmentInfo = m.prepareAttachments(msg.BinaryContent(), width)

	// 直接返回渲染内容
	if len(attachmentInfo) > 0 {
		return m.renderMessage("", msg.Content().String(), true, isFocused, width, attachmentInfo...)
	} else {
		return m.renderMessage("", msg.Content().String(), true, isFocused, width)
	}
}

// renderAssistantMessageContent 渲染助手消息内容
func (m *MessagesComponent) renderAssistantMessageContent(
	msg message.Message,
	msgIndex int,
	allMessages []message.Message,
	focusedUIMessageId string,
	isSummary bool,
	width int,
) []string {
	var contents []string
	content := msg.Content().String()
	thinkingContent := msg.ReasoningContent().Thinking
	finished := msg.IsFinished()
	finishData := msg.FinishPart()
	info := []string{}

	t := theme.CurrentTheme()
	baseStyle := styles.BaseStyle()

	// Add finish info if available
	if finished {
		switch finishData.Reason {
		case message.FinishReasonEndTurn:
			took := m.formatTimestampDiff(msg.CreatedAt, finishData.Time)
			info = append(info, baseStyle.
				Width(width-1).
				Foreground(t.TextMuted()).
				Render(fmt.Sprintf(" %s (%s)", models.SupportedModels[msg.Model].Name, took)),
			)
		case message.FinishReasonCanceled:
			info = append(info, baseStyle.
				Width(width-1).
				Foreground(t.TextMuted()).
				Render(fmt.Sprintf(" %s (%s)", models.SupportedModels[msg.Model].Name, "canceled")),
			)
		case message.FinishReasonError:
			info = append(info, baseStyle.
				Width(width-1).
				Foreground(t.TextMuted()).
				Render(fmt.Sprintf(" %s (%s)", models.SupportedModels[msg.Model].Name, "error")),
			)
		case message.FinishReasonPermissionDenied:
			info = append(info, baseStyle.
				Width(width-1).
				Foreground(t.TextMuted()).
				Render(fmt.Sprintf(" %s (%s)", models.SupportedModels[msg.Model].Name, "permission denied")),
			)
		}
	}

	if content != "" || (finished && finishData.Reason == message.FinishReasonEndTurn) {
		if content == "" {
			content = "*Finished without output*"
		}

		if isSummary {
			info = append(info, baseStyle.Width(width-1).Foreground(t.TextMuted()).Render(" (summary)"))
		}

		renderedContent := m.renderMessage(thinkingContent, content, false, true, width, info...)
		contents = append(contents, renderedContent)
	} else if thinkingContent != "" {
		// Render the thinking content
		renderedContent := m.renderMessage(thinkingContent, "", false, msg.Id == focusedUIMessageId, width)
		contents = append(contents, renderedContent)
	}

	// 渲染工具调用
	for i, toolCall := range msg.ToolCalls() {
		toolContent := m.renderToolMessageContent(toolCall, allMessages, focusedUIMessageId, false, width, i+1)
		contents = append(contents, toolContent)
	}

	return contents
}

// renderSystemMessageContent 渲染系统消息内容
func (m *MessagesComponent) renderSystemMessageContent(msg message.Message, isFocused bool, width int) string {
	// 统一使用renderMessage进行渲染，保持结构一致性
	return m.renderMessage("", msg.Content().Text, false, isFocused, width)
}

// renderToolMessageContent 渲染工具消息内容
func (m *MessagesComponent) renderToolMessageContent(
	toolCall message.ToolCall,
	allMessages []message.Message,
	focusedUIMessageId string,
	nested bool,
	width int,
	position int,
) string {
	if nested {
		width = width - 3
	}

	t := theme.CurrentTheme()
	baseStyle := styles.BaseStyle()

	style := baseStyle.
		Width(width - 1).
		BorderLeft(true).
		BorderStyle(lipgloss.ThickBorder()).
		PaddingLeft(1).
		BorderForeground(t.TextMuted())

	response := m.findToolResponse(toolCall.Id, allMessages)
	toolNameText := baseStyle.Foreground(t.TextMuted()).
		Render(fmt.Sprintf("%s: ", m.toolName(toolCall.Name)))

	if !toolCall.Finished {
		// 获取工具正在执行的动作描述
		toolAction := m.getToolAction(toolCall.Name)

		progressText := baseStyle.
			Width(width - 2 - lipgloss.Width(toolNameText)).
			Foreground(t.TextMuted()).
			Render(fmt.Sprintf("%s", toolAction))

		return style.Render(lipgloss.JoinHorizontal(lipgloss.Left, toolNameText, progressText))
	}

	params := m.renderToolParams(width-2-lipgloss.Width(toolNameText), toolCall)
	responseContent := ""
	if response != nil {
		responseContent = m.renderToolResponse(toolCall, *response, width-2)
		responseContent = strings.TrimSuffix(responseContent, "\n")
	} else {
		responseContent = baseStyle.
			Italic(true).
			Width(width - 2).
			Foreground(t.TextMuted()).
			Render("Waiting for response...")
	}

	parts := []string{}
	if !nested {
		formattedParams := baseStyle.
			Width(width - 2 - lipgloss.Width(toolNameText)).
			Foreground(t.TextMuted()).
			Render(params)

		parts = append(parts, lipgloss.JoinHorizontal(lipgloss.Left, toolNameText, formattedParams))
	} else {
		prefix := baseStyle.
			Foreground(t.TextMuted()).
			Render(" └ ")
		formattedParams := baseStyle.
			Width(width - 2 - lipgloss.Width(toolNameText)).
			Foreground(t.TextMuted()).
			Render(params)
		parts = append(parts, lipgloss.JoinHorizontal(lipgloss.Left, prefix, toolNameText, formattedParams))
	}

	if toolCall.Name == tools.TaskToolName {
		taskMessages, _ := m.app.GetMessageService().List(context.Background(), toolCall.Id)
		toolCalls := []message.ToolCall{}
		for _, v := range taskMessages {
			toolCalls = append(toolCalls, v.ToolCalls()...)
		}
		for _, call := range toolCalls {
			nestedContent := m.renderToolMessageContent(call, []message.Message{}, focusedUIMessageId, true, width, 0)
			parts = append(parts, nestedContent)
		}
	}
	if responseContent != "" && !nested {
		parts = append(parts, responseContent)
	}

	content := style.Render(
		lipgloss.JoinVertical(
			lipgloss.Left,
			parts...,
		),
	)
	if nested {
		content = lipgloss.JoinVertical(
			lipgloss.Left,
			parts...,
		)
	}

	return content
}

// 辅助方法 - 保留必要的工具和格式化函数

func (m *MessagesComponent) formatTimestampDiff(start, end int64) string {
	diffSeconds := float64(end-start) / 1000.0 // Convert to seconds
	if diffSeconds < 1 {
		return fmt.Sprintf("%dms", int(diffSeconds*1000))
	}
	if diffSeconds < 60 {
		return fmt.Sprintf("%.1fs", diffSeconds)
	}
	return fmt.Sprintf("%.1fm", diffSeconds/60)
}

// findToolResponse - 复用原始TUI的工具响应查找逻辑
func (m *MessagesComponent) findToolResponse(toolCallID string, futureMessages []message.Message) *message.ToolResult {
	for _, msg := range futureMessages {
		for _, result := range msg.ToolResults() {
			if result.ToolCallId == toolCallID {
				return &result
			}
		}
	}
	return nil
}

// toolName - 获取工具的友好名称
func (m *MessagesComponent) toolName(name string) string {
	switch name {
	case tools.TaskToolName:
		return "Task"
	case tools.BashToolName:
		return "Bash"
	case tools.EditToolName:
		return "Edit"
	case tools.WebFetchToolName:
		return "Fetch"
	case tools.GlobToolName:
		return "Glob"
	case tools.GrepToolName:
		return "Grep"
	case tools.LSToolName:
		return "List"
	case tools.ReadToolName:
		return "View"
	case tools.WriteToolName:
		return "Write"
	case tools.PatchToolName:
		return "Patch"
	}
	return name
}

// getToolAction - 获取工具正在执行的动作描述
func (m *MessagesComponent) getToolAction(name string) string {
	switch name {
	case tools.TaskToolName:
		return "Preparing prompt..."
	case tools.BashToolName:
		return "Building command..."
	case tools.EditToolName:
		return "Preparing edit..."
	case tools.WebFetchToolName:
		return "Writing fetch..."
	case tools.GlobToolName:
		return "Finding files..."
	case tools.GrepToolName:
		return "Searching content..."
	case tools.LSToolName:
		return "Listing directory..."
	case tools.ReadToolName:
		return "Reading file..."
	case tools.WriteToolName:
		return "Preparing write..."
	case tools.PatchToolName:
		return "Preparing patch..."
	}
	return "Working..."
}

// renderParams - 渲染工具参数
func (m *MessagesComponent) renderParams(paramsWidth int, params ...string) string {
	if len(params) == 0 {
		return ""
	}
	mainParam := params[0]
	if len(mainParam) > paramsWidth {
		mainParam = mainParam[:paramsWidth-3] + "..."
	}

	if len(params) == 1 {
		return mainParam
	}
	otherParams := params[1:]

	if len(otherParams)%2 != 0 {
		otherParams = append(otherParams, "")
	}

	pairs := []string{}
	for i := 0; i < len(otherParams); i += 2 {
		key := otherParams[i]
		value := otherParams[i+1]
		if value == "" {
			pairs = append(pairs, key)
		} else {
			pairs = append(pairs, fmt.Sprintf("%s=%s", key, value))
		}
	}

	if len(pairs) == 0 {
		return mainParam
	}

	otherParamsText := fmt.Sprintf("(%s)", strings.Join(pairs, " "))
	available := paramsWidth - len(mainParam) - 1
	if available > 0 && len(otherParamsText) > available {
		otherParamsText = otherParamsText[:available-3] + "...)"
	}

	if available <= 0 {
		return mainParam
	}

	return fmt.Sprintf("%s %s", mainParam, otherParamsText)
}

// renderToolParams - 根据工具类型渲染工具参数
func (m *MessagesComponent) renderToolParams(paramsWidth int, toolCall message.ToolCall) string {
	switch toolCall.Name {
	case tools.BashToolName:
		params, _ := specs.BashSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.Command)
	case tools.EditToolName:
		params, _ := specs.EditSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.FilePath)
	case tools.WebFetchToolName:
		params, _ := specs.WebFetchSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.URL)
	case tools.GlobToolName:
		params, _ := specs.GlobSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.Pattern)
	case tools.GrepToolName:
		params, _ := specs.GrepSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.Pattern, "path", params.Path)
	case tools.LSToolName:
		params, _ := specs.LsSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.Path)
	case tools.ReadToolName:
		params, _ := specs.ReadSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.FilePath)
	case tools.WriteToolName:
		params, _ := specs.WriteSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.FilePath)
	case tools.PatchToolName:
		params, _ := specs.PatchSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.PatchText)
	case tools.TaskToolName:
		params, _ := specs.TaskSpec.GetParams(toolCall.Input)
		return m.renderParams(paramsWidth, params.Description)
	}
	return ""
}

// truncateHeight - 截断内容到指定高度
func (m *MessagesComponent) truncateHeight(text string, maxHeight int) string {
	lines := strings.Split(text, "\n")
	if len(lines) <= maxHeight {
		return text
	}
	truncated := strings.Join(lines[:maxHeight], "\n")
	return truncated + "\n... (truncated)"
}

// renderToolResponse - 渲染工具响应结果
func (m *MessagesComponent) renderToolResponse(toolCall message.ToolCall, response message.ToolResult, width int) string {
	t := theme.CurrentTheme()
	baseStyle := styles.BaseStyle()

	if response.IsError {
		errContent := fmt.Sprintf("Error: %s", strings.ReplaceAll(response.Content, "\n", " "))
		errContent = ansi.Truncate(errContent, width-1, "...")
		return baseStyle.
			Width(width).
			Foreground(t.Error()).
			Render(errContent)
	}

	resultContent := m.truncateHeight(response.Content, config.MaxResultHeight)
	switch toolCall.Name {
	case tools.TaskToolName:
		return m.toMarkdown(resultContent, false, width)
	case tools.BashToolName:
		resultContent = fmt.Sprintf("```bash\n%s\n```", resultContent)
		return m.toMarkdown(resultContent, true, width)
	case tools.EditToolName:
		metadata, _ := specs.EditSpec.LoadResponseMetadata(response.Metadata)
		truncDiff := m.truncateHeight(metadata.Diff, config.MaxResultHeight)
		formattedDiff, _ := diff.FormatDiff(truncDiff, diff.WithTotalWidth(width))
		return formattedDiff
	case tools.WebFetchToolName:
		mdFormat := "markdown"
		resultContent = fmt.Sprintf("```%s\n%s\n```", mdFormat, resultContent)
		return m.toMarkdown(resultContent, true, width)
	case tools.GlobToolName:
		return baseStyle.Width(width).Foreground(t.TextMuted()).Render(resultContent)
	case tools.GrepToolName:
		return baseStyle.Width(width).Foreground(t.TextMuted()).Render(resultContent)
	case tools.LSToolName:
		return baseStyle.Width(width).Foreground(t.TextMuted()).Render(resultContent)
	case tools.ReadToolName:
		resultContent = fmt.Sprintf("```\n%s\n```", resultContent)
		return m.toMarkdown(resultContent, true, width)
	case tools.WriteToolName:
		return baseStyle.Width(width).Foreground(t.TextMuted()).Render(resultContent)
	case tools.PatchToolName:
		return baseStyle.Width(width).Foreground(t.TextMuted()).Render(resultContent)
	}
	return resultContent
}

// prepareAttachments 统一处理附件渲染
func (m *MessagesComponent) prepareAttachments(attachments []message.BinaryContent, width int) []string {
	if len(attachments) == 0 {
		return nil
	}

	t := theme.CurrentTheme()
	attachmentStyles := styles.BaseStyle().
		MarginLeft(1).
		Foreground(t.Text())

	var styledAttachments []string
	for _, attachment := range attachments {
		file := filepath.Base(attachment.Path)
		var filename string
		if len(file) > 10 {
			filename = fmt.Sprintf(" %s %s...", styles.DocumentIcon, file[0:7])
		} else {
			filename = fmt.Sprintf(" %s %s", styles.DocumentIcon, file)
		}
		styledAttachments = append(styledAttachments, attachmentStyles.Render(filename))
	}

	attachmentContent := styles.BaseStyle().Width(width).Render(lipgloss.JoinHorizontal(lipgloss.Left, styledAttachments...))
	return []string{attachmentContent}
}
