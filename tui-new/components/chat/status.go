package chat

import (
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	tuinewconfig "github.com/qoder-ai/qoder-cli/tui-new/config"
	"github.com/qoder-ai/qoder-cli/tui-new/styles"
	"github.com/qoder-ai/qoder-cli/tui-new/theme"
	"github.com/qoder-ai/qoder-cli/tui-new/util"
)

// StatusComponent 状态组件
type StatusComponent struct {
	width      int
	info       util.InfoMsg
	messageTTL time.Duration
	session    core.Session
	app        app.App
}

// clearMessageCmd 清除消息命令
func (m *StatusComponent) clearMessageCmd(ttl time.Duration) tea.Cmd {
	return tea.Tick(ttl, func(time.Time) tea.Msg {
		return util.ClearStatusMsg{}
	})
}

func (m *StatusComponent) Init() tea.Cmd {
	return nil
}

func (m *StatusComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		return m, nil
	case util.InfoMsg:
		m.info = msg
		ttl := msg.TTL
		if ttl == 0 {
			ttl = m.messageTTL
		}
		return m, m.clearMessageCmd(ttl)
	case util.ClearStatusMsg:
		m.info = util.InfoMsg{}
	}
	return m, nil
}

func (m *StatusComponent) View() string {
	h := m.renderHelpWidget()
	mi := m.renderModelInfo()
	tc := m.renderTokenAndCost()

	tokenInfoWidth := 0
	if tc != "" {
		tokenInfoWidth = lipgloss.Width(tc) + 1
	}

	// 考虑到2字符的padding，实际可用宽度需要减去2
	availableWidth := max(0, m.width-2-lipgloss.Width(h)-lipgloss.Width(mi)-tokenInfoWidth)
	i := m.renderInformation(availableWidth)

	status := mi // 模型信息
	status += tc // 令牌和成本
	status += i  // 信息
	status += h  // 帮助信息

	// 应用一致的2字符padding，与消息组件和编辑器组件保持一致
	statusStyle := lipgloss.NewStyle().PaddingLeft(2)
	return statusStyle.Render(status)
}

// renderInformation 渲染信息区域
func (s *StatusComponent) renderInformation(maxWidth int) string {
	t := theme.CurrentTheme()
	if s.info.Msg != "" {
		infoStyle := styles.BaseStyle().
			Width(maxWidth)

		switch s.info.Type {
		case util.InfoTypeInfo:
			infoStyle = infoStyle.Background(t.Info())
		case util.InfoTypeWarn:
			infoStyle = infoStyle.Background(t.Warning())
		case util.InfoTypeError:
			infoStyle = infoStyle.Background(t.Error())
		}

		infoWidth := maxWidth - 10
		// 如果消息长度超过可用宽度则截断
		msg := s.info.Msg
		if len(msg) > infoWidth && infoWidth > 0 {
			msg = msg[:infoWidth] + "..."
		}

		return infoStyle.Render(msg)
	}
	return styles.BaseStyle().
		Foreground(t.Text()).
		Width(maxWidth).
		Render("")
}

// renderTokenAndCost 渲染令牌和成本信息
func (m *StatusComponent) renderTokenAndCost() string {
	if m.session.Id != "" {
		return ""
	}

	t := theme.CurrentTheme()

	modelID := m.app.GetConfig().Agents[config.AgentCoder].Model
	model := models.SupportedModels[modelID]

	totalTokens := m.session.PromptTokens + m.session.CompletionTokens
	tokens := formatTokensAndCost(totalTokens, model.ContextWindow, m.session.Cost)

	tokensStyle := styles.Padded().
		Background(t.Text()).
		Foreground(t.BackgroundDarker())

	percentage := (float64(totalTokens) / float64(model.ContextWindow)) * 100
	if percentage > tuinewconfig.DefaultFormatConfig.TokenWarningPercentage {
		tokensStyle = tokensStyle.Background(t.Warning())
	}
	return tokensStyle.Render(tokens)
}

// renderModelInfo 渲染模型信息
func (m *StatusComponent) renderModelInfo() string {
	t := theme.CurrentTheme()
	cfg := m.app.GetConfig()

	coder, ok := cfg.Agents[config.AgentCoder]
	if !ok {
		return "Unknown"
	}
	model := models.SupportedModels[coder.Model]

	return styles.Padded().
		Background(t.Secondary()).
		Foreground(t.Background()).
		Render(model.Name)
}

// renderHelpWidget 渲染帮助小部件
func (m *StatusComponent) renderHelpWidget() string {
	t := theme.CurrentTheme()

	return styles.Padded().
		Background(t.TextMuted()).
		Foreground(t.BackgroundDarker()).
		Bold(true).
		Render(tuinewconfig.DefaultUITexts.HeaderOpenText)
}

// formatTokensAndCost 格式化令牌和成本信息
func formatTokensAndCost(tokens, contextWindow int64, cost float64) string {
	// 以人类可读的格式格式化令牌数（例如：110K, 1.2M）
	var formattedTokens string
	cfg := tuinewconfig.DefaultFormatConfig

	switch {
	case tokens >= cfg.TokenMegaThreshold:
		formattedTokens = fmt.Sprintf("%.1fM", float64(tokens)/float64(cfg.TokenMegaThreshold))
	case tokens >= cfg.TokenKiloThreshold:
		formattedTokens = fmt.Sprintf("%.1fK", float64(tokens)/float64(cfg.TokenKiloThreshold))
	default:
		formattedTokens = fmt.Sprintf("%d", tokens)
	}

	// 如果存在.0后缀则移除
	if strings.HasSuffix(formattedTokens, ".0K") {
		formattedTokens = strings.Replace(formattedTokens, ".0K", "K", 1)
	}
	if strings.HasSuffix(formattedTokens, ".0M") {
		formattedTokens = strings.Replace(formattedTokens, ".0M", "M", 1)
	}

	// 使用$符号和指定小数位格式化成本
	formattedCost := fmt.Sprintf("$%.2f", cost)

	percentage := (float64(tokens) / float64(contextWindow)) * 100
	if percentage > cfg.TokenWarningPercentage {
		// 添加警告图标和百分比
		formattedTokens = fmt.Sprintf("%s(%d%%)", styles.WarningIcon, int(percentage))
	}

	return fmt.Sprintf("Context: %s, Cost: %s", formattedTokens, formattedCost)
}

// NewStatusCmp 创建新的状态组件
func NewStatusCmp(app app.App) *StatusComponent {
	return &StatusComponent{
		app:        app,
		messageTTL: tuinewconfig.DefaultMessageTTL,
	}
}
