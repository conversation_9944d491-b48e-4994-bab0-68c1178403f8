package chat

import (
	"fmt"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/tui-new/components/dialog"
	"github.com/qoder-ai/qoder-cli/tui-new/config"
	"github.com/qoder-ai/qoder-cli/tui-new/styles"
	"github.com/qoder-ai/qoder-cli/tui-new/theme"
)

// InteractionOption 表示一个选择选项
type InteractionOption struct {
	Text   string
	Value  string
	Hotkey string
	Action dialog.PermissionAction
}

// InteractionCompleteMsg 交互完成消息
type InteractionCompleteMsg struct {
	Type     config.InteractionType
	Selected string
	Action   dialog.PermissionAction
	Request  *core.PermissionRequest
}

// InteractionComponent 专门处理各种交互的组件
type InteractionComponent struct {
	width  int
	height int

	// 交互状态
	interactionType config.InteractionType
	title           string
	description     string
	options         []InteractionOption
	selectedIndex   int

	// 权限相关
	permissionRequest *core.PermissionRequest
}

// NewInteractionComponent 创建新的交互组件
func NewInteractionComponent() *InteractionComponent {
	return &InteractionComponent{
		selectedIndex: 0,
	}
}

// Init 初始化组件
func (c *InteractionComponent) Init() tea.Cmd {
	return nil
}

// Update 更新组件状态
func (c *InteractionComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		c.width = msg.Width

	case tea.KeyMsg:
		return c.handleKeyInput(msg)
	}

	return c, nil
}

// handleKeyInput 处理键盘输入
func (c *InteractionComponent) handleKeyInput(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		c.selectedIndex = max(0, c.selectedIndex-1)
	case "down", "j":
		c.selectedIndex = min(len(c.options)-1, c.selectedIndex+1)
	case "enter":
		return c.confirmSelection()
	case "1", "2", "3":
		// 数字快捷键
		if idx := int(msg.String()[0] - '1'); idx < len(c.options) {
			c.selectedIndex = idx
			return c.confirmSelection()
		}
	case "esc", "q":
		// ESC键取消交互，返回默认拒绝
		if len(c.options) > 0 {
			c.selectedIndex = len(c.options) - 1 // 选择最后一个选项（通常是拒绝）
			return c.confirmSelection()
		}
	}

	return c, nil
}

// confirmSelection 确认选择
func (c *InteractionComponent) confirmSelection() (tea.Model, tea.Cmd) {
	if c.selectedIndex >= len(c.options) {
		return c, nil
	}

	selectedOption := c.options[c.selectedIndex]

	// 返回交互完成消息
	return c, func() tea.Msg {
		return InteractionCompleteMsg{
			Type:     c.interactionType,
			Selected: selectedOption.Value,
			Action:   selectedOption.Action,
			Request:  c.permissionRequest,
		}
	}
}

// View 渲染组件
func (c *InteractionComponent) View() string {
	if c.interactionType == "" {
		// 如果没有交互，返回空
		return ""
	}

	// 计算并更新实际高度
	c.height = c.calculateInteractionHeight()

	t := theme.CurrentTheme()
	var parts []string

	// 标题
	titleStyle := styles.BaseStyle().
		Bold(true).
		Foreground(t.Secondary())
	parts = append(parts, titleStyle.Render(c.title))

	// 描述
	if c.description != "" {
		descStyle := styles.BaseStyle().
			Foreground(t.Text())
		parts = append(parts, "", descStyle.Render(c.description), "")
	}

	// 选项列表
	for i, option := range c.options {
		optionStyle := styles.BaseStyle().
			PaddingLeft(2)

		prefix := "  "

		if i == c.selectedIndex {
			// 高亮当前选择
			optionStyle = optionStyle.
				Foreground(t.Primary()).
				Bold(true).
				Underline(true)
			prefix = "▶ "
		}

		optionText := fmt.Sprintf("%s%s. %s", prefix, option.Hotkey, option.Text)
		parts = append(parts, optionStyle.Render(optionText))
	}

	// 提示信息
	hintStyle := styles.BaseStyle().
		Foreground(t.TextMuted()).
		Italic(true)
	parts = append(parts, "", hintStyle.Render(config.DefaultUITexts.NavigationHint))

	// 添加边框以清晰标识交互状态
	borderStyle := styles.BaseStyle().
		Border(lipgloss.RoundedBorder(), true, true, true, true).
		BorderForeground(t.Primary()).
		Padding(1, 2)

	content := lipgloss.JoinVertical(lipgloss.Left, parts...)
	return borderStyle.Render(content)
}

// SetPermissionRequest 设置权限请求交互
func (c *InteractionComponent) SetPermissionRequest(req core.PermissionRequest) {
	c.interactionType = config.PermissionInteraction
	c.title = config.DefaultUITexts.PermissionTitle
	c.description = req.Description
	c.permissionRequest = &req
	c.selectedIndex = 0

	// 设置权限选项
	c.options = []InteractionOption{
		{
			Text:   config.DefaultUITexts.PermissionAllowText,
			Value:  "allow",
			Hotkey: "1",
			Action: dialog.PermissionAllow,
		},
		{
			Text:   config.DefaultUITexts.PermissionAllowSession,
			Value:  "allow_session",
			Hotkey: "2",
			Action: dialog.PermissionAllowForSession,
		},
		{
			Text:   config.DefaultUITexts.PermissionDenyText,
			Value:  "deny",
			Hotkey: "3",
			Action: dialog.PermissionDeny,
		},
	}
}

// SetConfirmation 设置确认对话框交互
func (c *InteractionComponent) SetConfirmation(title, description string, options []InteractionOption) {
	c.interactionType = config.ConfirmInteraction
	c.title = title
	c.description = description
	c.options = options
	c.selectedIndex = 0
	c.permissionRequest = nil
}

// Clear 清除交互状态
func (c *InteractionComponent) Clear() {
	c.interactionType = ""
	c.title = ""
	c.description = ""
	c.options = nil
	c.selectedIndex = 0
	c.permissionRequest = nil
}

// IsActive 检查是否有活跃的交互
func (c *InteractionComponent) IsActive() bool {
	return c.interactionType != ""
}

// GetSize 实现Sizeable接口
func (c *InteractionComponent) GetSize() (int, int) {
	if !c.IsActive() {
		return c.width, 0 // 如果没有交互，高度为0
	}
	return c.width, c.calculateInteractionHeight()
}

// SetSize 实现Sizeable接口
func (c *InteractionComponent) SetSize(width, height int) {
	c.width = width
	c.height = height
}

// calculateInteractionHeight 计算交互框的实际高度
func (c *InteractionComponent) calculateInteractionHeight() int {
	if !c.IsActive() {
		return 0
	}

	// 基础高度：标题 + 描述 + 选项 + 提示 + 边框
	height := 1 // 标题

	if c.description != "" {
		height += 3 // 空行 + 描述 + 空行
	}

	height += len(c.options) // 选项列表
	height += 2              // 空行 + 提示
	height += 4              // 边框和padding (上下各2行)

	return height
}

// max 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
