package chat

import (
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qoder-cli/tui-new/styles"
)

// HintComponent 提供快捷键提示功能
type HintComponent struct {
	width   int
	height  int
	state   HintState
	content string
	focused bool
}

// HintState 定义提示状态
type HintState int

const (
	HintStateDefault HintState = iota
	HintStateExpanded
)

// hintContent 状态内容映射
var hintContent = map[HintState]string{
	HintStateDefault: "? for shortcuts",
	HintStateExpanded: "" +
		"! for bash mode      # to memorize                   ctrl + r for verbose output      \\⏎ for newline\n" +
		"/ for commands       double tap esc to clear         input ctrl + _ to undo\n" +
		"@ for file paths     shift + tab to auto-accept      editsctrl + z to suspend",
}

// EditorContentChangedMsg 编辑器内容变化消息
type EditorContentChangedMsg struct {
	Content  string
	IsEmpty  bool
	LastChar rune
	Action   EditorAction
}

// EditorAction 编辑器操作类型
type EditorAction int

const (
	EditorActionInput EditorAction = iota
	EditorActionBackspace
	EditorActionClear
	EditorActionQuestionMark // 在空编辑器中按下?键
)

// HintStateChangeMsg 提示状态变化消息
type HintStateChangeMsg struct {
	NewState HintState
}

// HintHeightChangeMsg 提示组件高度变化消息
type HintHeightChangeMsg struct {
	OldHeight int
	NewHeight int
}

// getHeightForState 根据状态返回对应高度
func getHeightForState(state HintState) int {
	switch state {
	case HintStateDefault:
		return 1 // 默认状态：1行
	case HintStateExpanded:
		return 3 // 展开状态：3行
	default:
		return 1
	}
}

// NewHintComponent 创建新的提示组件
func NewHintComponent() *HintComponent {
	return &HintComponent{
		state:   HintStateDefault,
		content: hintContent[HintStateDefault],
		height:  getHeightForState(HintStateDefault), // 初始高度为1行
	}
}

// Init 初始化组件
func (h *HintComponent) Init() tea.Cmd {
	return nil
}

// Update 更新组件状态
func (h *HintComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		h.width = msg.Width
		return h, nil

	case EditorContentChangedMsg:
		// 根据编辑器内容变化决定状态切换
		newState := h.determineNewState(msg)
		if newState != h.state {
			heightChangeCmd := h.setState(newState)
			stateChangeCmd := func() tea.Msg {
				return HintStateChangeMsg{NewState: newState}
			}
			// 批量执行状态变化和高度变化命令
			if heightChangeCmd != nil {
				return h, tea.Batch(stateChangeCmd, heightChangeCmd)
			}
			return h, stateChangeCmd
		}
		return h, nil

	case HintStateChangeMsg:
		heightChangeCmd := h.setState(msg.NewState)
		return h, heightChangeCmd
	}

	return h, nil
}

// View 渲染组件视图
func (h *HintComponent) View() string {
	if h.width == 0 {
		return ""
	}

	//t := theme.CurrentTheme()
	style := styles.BaseStyle().
		Foreground(lipgloss.Color("240")).
		Padding(0, 1).
		Width(h.width).
		Height(h.height)

	return style.Render(h.content)
}

// SetSize 设置组件尺寸
func (h *HintComponent) SetSize(width, height int) tea.Cmd {
	h.width = width
	h.height = height
	return nil
}

// GetSize 获取组件尺寸
func (h *HintComponent) GetSize() (int, int) {
	return h.width, h.height
}

// IsFocused 检查组件是否获得焦点
func (h *HintComponent) IsFocused() bool {
	return h.focused
}

// Focus 设置组件焦点
func (h *HintComponent) Focus() tea.Cmd {
	h.focused = true
	return nil
}

// Blur 取消组件焦点
func (h *HintComponent) Blur() tea.Cmd {
	h.focused = false
	return nil
}

// determineNewState 根据编辑器内容变化确定新状态
func (h *HintComponent) determineNewState(msg EditorContentChangedMsg) HintState {
	// 特殊处理：用户在空编辑器中按下了?键
	if msg.Action == EditorActionQuestionMark {
		// 如果当前是默认状态，则展开；如果已经展开，则收起
		if h.state == HintStateDefault {
			return HintStateExpanded
		} else {
			return HintStateDefault
		}
	}

	// 收起条件：编辑器不为空，或者有其他输入/删除操作
	if !msg.IsEmpty || msg.Action == EditorActionInput || msg.Action == EditorActionBackspace || msg.Action == EditorActionClear {
		return HintStateDefault
	}

	// 保持当前状态
	return h.state
}

// setState 设置新状态并更新内容和高度
func (h *HintComponent) setState(newState HintState) tea.Cmd {
	oldHeight := h.height
	newHeight := getHeightForState(newState)

	h.state = newState
	h.height = newHeight
	if content, exists := hintContent[newState]; exists {
		h.content = content
	}

	// 如果高度发生变化，发送高度变化消息
	if oldHeight != newHeight {
		return func() tea.Msg {
			return HintHeightChangeMsg{
				OldHeight: oldHeight,
				NewHeight: newHeight,
			}
		}
	}

	return nil
}

// GetState 获取当前状态
func (h *HintComponent) GetState() HintState {
	return h.state
}
