package chat

import (
	"fmt"
	"os"
	"os/exec"
	"slices"
	"strings"
	"unicode"

	"github.com/qoder-ai/qoder-cli/core/app"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/tui-new/components/dialog"
	"github.com/qoder-ai/qoder-cli/tui-new/config"
	"github.com/qoder-ai/qoder-cli/tui-new/styles"
	"github.com/qoder-ai/qoder-cli/tui-new/theme"
	"github.com/qoder-ai/qoder-cli/tui-new/util"

	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/bubbles/textarea"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/qoder-ai/qoder-cli/core"
	"github.com/qoder-ai/qoder-cli/core/message"
)

// SendMsg 发送消息事件
type SendMsg struct {
	Text        string
	Attachments []message.Attachment
}

// EditorComponent 编辑器组件
type EditorComponent struct {
	width       int
	height      int
	app         app.App
	session     core.Session
	textarea    textarea.Model
	attachments []message.Attachment
	deleteMode  bool
	focused     bool
	escCount    int // ESC键按下次数，用于双击清空功能
}

// EditorKeyMaps 编辑器快捷键映射
type EditorKeyMaps struct {
	Send       key.Binding
	OpenEditor key.Binding
}

// DeleteAttachmentKeyMaps 删除附件快捷键映射
type DeleteAttachmentKeyMaps struct {
	AttachmentDeleteMode key.Binding
	Escape               key.Binding
	DeleteAllAttachments key.Binding
}

// 编辑器按键绑定
var editorMaps = EditorKeyMaps{
	Send:       config.DefaultKeyBindings.Send,
	OpenEditor: config.DefaultKeyBindings.OpenEditor,
}

// 删除按键绑定
var DeleteKeyMaps = DeleteAttachmentKeyMaps{
	AttachmentDeleteMode: config.DefaultKeyBindings.AttachmentDeleteMode,
	Escape:               config.DefaultKeyBindings.Escape,
	DeleteAllAttachments: config.DefaultKeyBindings.DeleteAllAttachments,
}

func (m *EditorComponent) Init() tea.Cmd {
	m.focused = true
	return textarea.Blink
}

func (m *EditorComponent) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		// 设置textarea宽度，留出padding和提示符空间
		m.textarea.SetWidth(m.width - 6) // 2(padding) + 2(prompt) + 2(margin)
		// 确保高度保持3行
		m.textarea.SetHeight(3)
		return m, nil
	case dialog.ThemeChangedMsg:
		m.textarea = createTextArea(&m.textarea)
	case dialog.CompletionSelectedMsg:
		existingValue := m.textarea.Value()
		modifiedValue := strings.Replace(existingValue, msg.SearchString, msg.CompletionValue, 1)

		m.textarea.SetValue(modifiedValue)
		return m, nil
	case dialog.AttachmentAddedMsg:
		if len(m.attachments) >= config.MaxAttachments {
			logging.ErrorPersist(fmt.Sprintf("无法添加超过 %d 个附件", config.MaxAttachments))
			return m, cmd
		}
		m.attachments = append(m.attachments, msg.Attachment)
	case tea.KeyMsg:
		// 记录操作前的内容状态
		oldContent := m.textarea.Value()

		// 处理双击ESC清空功能
		if msg.String() == "esc" {
			m.escCount++
			if m.escCount >= 2 {
				// 双击ESC，清空textarea内容
				m.textarea.SetValue("")
				m.escCount = 0
				// 发送内容变化消息
				cmd = tea.Batch(cmd, func() tea.Msg {
					return EditorContentChangedMsg{
						Content:  "",
						IsEmpty:  true,
						LastChar: 0,
						Action:   EditorActionClear,
					}
				})
				return m, cmd
			}
			// 单击ESC，退出删除模式
			m.deleteMode = false
			return m, nil
		} else {
			// 按其他键时重置ESC计数
			m.escCount = 0
		}

		// 特殊处理：在空编辑器中按?键，不输入到textarea，直接触发hint展开
		if oldContent == "" && msg.String() == "?" {
			// 发送特殊的问号触发消息
			cmd = tea.Batch(cmd, func() tea.Msg {
				return EditorContentChangedMsg{
					Content:  "",
					IsEmpty:  true,
					LastChar: '?',
					Action:   EditorActionQuestionMark,
				}
			})
			return m, cmd
		}

		// 特殊处理：在空编辑器中按退格键或其他收起键，触发hint收起
		if oldContent == "" {
			var shouldTriggerAction EditorAction
			var shouldSendMsg bool

			switch msg.String() {
			case "backspace":
				shouldTriggerAction = EditorActionBackspace
				shouldSendMsg = true
			case "delete":
				shouldTriggerAction = EditorActionBackspace
				shouldSendMsg = true
			case "space":
				// 空格键：让它正常输入，但同时发送消息收起hint
				shouldTriggerAction = EditorActionInput
				shouldSendMsg = false // 让textarea正常处理，后面会检测到内容变化
			}

			if shouldSendMsg {
				// 发送消息以收起hint，但不让按键进入textarea
				cmd = tea.Batch(cmd, func() tea.Msg {
					return EditorContentChangedMsg{
						Content:  "",
						IsEmpty:  true,
						LastChar: 0,
						Action:   shouldTriggerAction,
					}
				})
				return m, cmd
			}
		}

		// 更新textarea，但保持固定高度
		m.textarea, cmd = m.textarea.Update(msg)
		// 确保textarea保持3行高度
		m.textarea.SetHeight(3)

		// 检查内容是否发生变化
		newContent := m.textarea.Value()
		if oldContent != newContent {
			// 确定操作类型
			action := EditorActionInput
			if len(newContent) < len(oldContent) {
				action = EditorActionBackspace
			} else if newContent == "" {
				action = EditorActionClear
			}

			// 获取最后一个字符
			var lastChar rune
			if len(newContent) > 0 {
				lastChar = rune(newContent[len(newContent)-1])
			}

			// 发送内容变化消息
			contentChangeCmd := func() tea.Msg {
				return EditorContentChangedMsg{
					Content:  newContent,
					IsEmpty:  newContent == "",
					LastChar: lastChar,
					Action:   action,
				}
			}
			cmd = tea.Batch(cmd, contentChangeCmd)
		}

		if key.Matches(msg, DeleteKeyMaps.AttachmentDeleteMode) {
			m.deleteMode = true
			return m, nil
		}
		if key.Matches(msg, DeleteKeyMaps.DeleteAllAttachments) && m.deleteMode {
			m.deleteMode = false
			m.attachments = nil
			return m, nil
		}
		if m.deleteMode && len(msg.Runes) > 0 && unicode.IsDigit(msg.Runes[0]) {
			num := int(msg.Runes[0] - '0')
			m.deleteMode = false
			if num < 10 && len(m.attachments) > num {
				if num == 0 {
					m.attachments = m.attachments[num+1:]
				} else {
					m.attachments = slices.Delete(m.attachments, num, num+1)
				}
				return m, nil
			}
		}

		if key.Matches(msg, editorMaps.OpenEditor) {
			if m.app.GetCoderAgent().IsSessionBusy(m.session.Id) {
				return m, util.ReportWarn(config.DefaultUITexts.AgentBusyMessage)
			}
			return m, m.openEditor()
		}
		// 处理回车键
		if m.textarea.Focused() && key.Matches(msg, editorMaps.Send) {
			value := m.textarea.Value()
			if len(value) > 0 && value[len(value)-1] == '\\' {
				// 如果最后一个字符是反斜杠，移除它并添加换行
				m.textarea.SetValue(value[:len(value)-1] + "\n")
				return m, nil
			} else {
				// 否则，发送消息
				return m, m.send()
			}
		}

	}
	return m, cmd
}

func (m *EditorComponent) View() string {
	// 渲染固定高度的textarea输入区域
	inputContent := m.renderFixedHeightInput()

	// 渲染附件和其他信息
	var lines []string
	lines = append(lines, inputContent)

	// 添加附件显示
	if len(m.attachments) > 0 {
		attachmentLines := m.renderAttachments()
		lines = append(lines, attachmentLines)
	}

	// 应用一致的2字符padding，与消息组件保持一致
	content := strings.Join(lines, "\n")
	editorStyle := lipgloss.NewStyle().PaddingLeft(0)
	return editorStyle.Render(content)
}

// renderFixedHeightInput 渲染固定高度的文本输入区域
func (m *EditorComponent) renderFixedHeightInput() string {
	// 设置提示符样式
	textareaStyle := styles.BaseStyle()
	if m.focused {
		textareaStyle = textareaStyle.Foreground(lipgloss.Color("36"))
	} else {
		textareaStyle = textareaStyle.Foreground(lipgloss.Color("240"))
	}
	m.textarea.SetPromptFunc(2, func(lineIdx int) string {
		if lineIdx == 0 {
			return textareaStyle.Render("  > ")
		} else {
			return textareaStyle.Render("::: ")
		}
	})

	// 设置占位符
	if m.textarea.Value() == "" {
		m.textarea.Placeholder = "Type your message..."
	}

	return m.textarea.View()
}

func (m *EditorComponent) BindingKeys() []key.Binding {
	bindings := []key.Binding{}
	bindings = append(bindings, util.KeyMapToSlice(editorMaps)...)
	bindings = append(bindings, util.KeyMapToSlice(DeleteKeyMaps)...)
	return bindings
}

// renderAttachments 渲染附件列表
func (m *EditorComponent) renderAttachments() string {
	var styledAttachments []string
	t := theme.CurrentTheme()
	attachmentStyles := styles.BaseStyle().
		MarginLeft(1).
		Foreground(t.Text())
	for i, attachment := range m.attachments {
		var filename string
		if len(attachment.FileName) > 10 {
			filename = fmt.Sprintf(" %s %s...", styles.DocumentIcon, attachment.FileName[0:7])
		} else {
			filename = fmt.Sprintf(" %s %s", styles.DocumentIcon, attachment.FileName)
		}
		if m.deleteMode {
			filename = fmt.Sprintf("%d%s", i, filename)
		}
		styledAttachments = append(styledAttachments, attachmentStyles.Render(filename))
	}
	content := lipgloss.JoinHorizontal(lipgloss.Left, styledAttachments...)
	return content
}

// openEditor 打开外部编辑器
func (m *EditorComponent) openEditor() tea.Cmd {
	editor := os.Getenv("EDITOR")
	if editor == "" {
		editor = "vi"
	}

	tmpfile, err := os.CreateTemp("", "msg_*.md")
	if err != nil {
		return util.ReportError(err)
	}
	tmpfile.Close()
	c := exec.Command(editor, tmpfile.Name()) //nolint:gosec
	c.Stdin = os.Stdin
	c.Stdout = os.Stdout
	c.Stderr = os.Stderr
	return tea.ExecProcess(c, func(err error) tea.Msg {
		if err != nil {
			return util.ReportError(err)
		}
		content, err := os.ReadFile(tmpfile.Name())
		if err != nil {
			return util.ReportError(err)
		}
		if len(content) == 0 {
			return util.ReportWarn(config.DefaultUITexts.MessageEmptyWarning)
		}
		os.Remove(tmpfile.Name())
		attachments := m.attachments
		m.attachments = nil
		return SendMsg{
			Text:        string(content),
			Attachments: attachments,
		}
	})
}

// send 发送消息
func (m *EditorComponent) send() tea.Cmd {
	if m.app.GetCoderAgent().IsSessionBusy(m.session.Id) {
		return util.ReportWarn(config.DefaultUITexts.AgentBusyMessage)
	}

	value := m.textarea.Value()
	m.textarea.Reset()
	attachments := m.attachments

	m.attachments = nil
	if value == "" {
		return nil
	}
	return tea.Batch(
		util.CmdHandler(SendMsg{
			Text:        value,
			Attachments: attachments,
		}),
	)
}

func (m *EditorComponent) SetFocus(focused bool) {
	m.focused = focused
	if focused {
		m.textarea.Focus()
	} else {
		m.textarea.Blur()
	}
}

func (m *EditorComponent) IsFocused() bool {
	return m.focused
}

// createTextArea 创建文本区域
func createTextArea(existing *textarea.Model) textarea.Model {
	ta := textarea.New()
	ta.BlurredStyle.Base = styles.BaseStyle()
	ta.BlurredStyle.CursorLine = styles.BaseStyle()
	ta.BlurredStyle.Placeholder = styles.BaseStyle().Foreground(lipgloss.Color("240"))
	ta.BlurredStyle.Text = styles.BaseStyle()
	ta.FocusedStyle.Base = styles.BaseStyle()
	ta.FocusedStyle.CursorLine = styles.BaseStyle()
	ta.FocusedStyle.Placeholder = styles.BaseStyle().Foreground(lipgloss.Color("240"))
	ta.FocusedStyle.Text = styles.BaseStyle()

	ta.SetPromptFunc(2, func(lineIdx int) string {
		textareaStyle := styles.BaseStyle()
		if ta.Focused() {
			textareaStyle = textareaStyle.Foreground(lipgloss.Color("36"))
		} else {
			textareaStyle = textareaStyle.Foreground(lipgloss.Color("240"))
		}
		if lineIdx == 0 {
			return textareaStyle.Render("  > ")
		} else {
			return textareaStyle.Render("::: ")
		}
	})

	ta.ShowLineNumbers = false
	ta.CharLimit = -1

	// 设置固定高度为3行
	ta.SetHeight(3)
	// 启用光标移动功能
	ta.KeyMap.LineNext.SetEnabled(true)
	ta.KeyMap.LinePrevious.SetEnabled(true)
	ta.KeyMap.WordBackward.SetEnabled(true)
	ta.KeyMap.WordForward.SetEnabled(true)

	if existing != nil {
		ta.SetValue(existing.Value())
		ta.SetWidth(existing.Width())
		// 保持固定高度
		ta.SetHeight(3)
	}

	ta.Focus()
	return ta
}

// NewEditorComponent 创建新的编辑器组件
func NewEditorComponent(app app.App) *EditorComponent {
	ta := createTextArea(nil)
	return &EditorComponent{
		app:      app,
		textarea: ta,
	}
}
