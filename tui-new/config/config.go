package config

import (
	cmdCfg "github.com/qoder-ai/qoder-cli/cmd/config"
	"time"

	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/models"

	"github.com/charmbracelet/bubbles/key"
)

// 应用程序模式枚举
type AppMode string

const (
	NormalMode      AppMode = "normal"
	InteractionMode AppMode = "interaction"
)

// 焦点组件枚举
type FocusComponent string

const (
	FocusEditor   FocusComponent = "editor"
	FocusMessages FocusComponent = "messages"
)

// 交互类型枚举
type InteractionType string

const (
	PermissionInteraction InteractionType = "permission"
	ConfirmInteraction    InteractionType = "confirm"
)

// 布局配置常量
const (
	// 基础布局尺寸
	EditorHeight = 6 // 编辑器高度
	StatusHeight = 1 // 状态栏高度

	// 最小窗口尺寸
	MinWindowWidth  = 40 // 最小窗口宽度
	MinWindowHeight = 5  // 最小窗口高度

	// 内容限制
	MaxAttachments    = 5  // 最大附件数量
	MaxResultHeight   = 10 // 最大结果显示高度
	MinViewportHeight = 5  // 最小视口高度
)

// LayoutUpdateMsg 布局更新消息，用于协调各组件高度
type LayoutUpdateMsg struct {
	StatusHeight      int
	EditorHeight      int
	HintHeight        int
	InteractionHeight int
	IsInteractionMode bool // 是否为交互模式
}

// 时间配置常量
const (
	DefaultMessageTTL = 10 * time.Second       // 默认消息显示时间
	SpinnerTickRate   = 100 * time.Millisecond // 旋转器刷新间隔
)

// 键盘快捷键配置
const (
	QuitKey = "q" // 退出键
)

// 键盘绑定映射
type KeyBindings struct {
	// 全局快捷键
	Logs          key.Binding
	Quit          key.Binding
	Help          key.Binding
	SwitchSession key.Binding
	Commands      key.Binding
	Filepicker    key.Binding
	Models        key.Binding
	SwitchTheme   key.Binding
	SwitchFocus   key.Binding

	// 编辑器快捷键
	Send       key.Binding
	OpenEditor key.Binding

	// 附件删除快捷键
	AttachmentDeleteMode key.Binding
	Escape               key.Binding
	DeleteAllAttachments key.Binding
}

// 默认键盘绑定配置
var DefaultKeyBindings = KeyBindings{
	// 全局快捷键
	Logs: key.NewBinding(
		key.WithKeys("ctrl+l"),
		key.WithHelp("ctrl+l", "查看日志"),
	),
	Quit: key.NewBinding(
		key.WithKeys("ctrl+c"),
		key.WithHelp("ctrl+c", "退出"),
	),
	Help: key.NewBinding(
		key.WithKeys("ctrl+h"),
		key.WithHelp("ctrl+h", "帮助"),
	),
	SwitchSession: key.NewBinding(
		key.WithKeys("ctrl+p"),
		key.WithHelp("ctrl+p", "切换会话"),
	),
	Commands: key.NewBinding(
		key.WithKeys("ctrl+k"),
		key.WithHelp("ctrl+k", "命令搜索"),
	),
	Filepicker: key.NewBinding(
		key.WithKeys("ctrl+f"),
		key.WithHelp("ctrl+f", "选择文件上传"),
	),
	Models: key.NewBinding(
		key.WithKeys("ctrl+o"),
		key.WithHelp("ctrl+o", "模型选择"),
	),
	SwitchTheme: key.NewBinding(
		key.WithKeys("ctrl+t"),
		key.WithHelp("ctrl+t", "切换主题"),
	),
	SwitchFocus: key.NewBinding(
		key.WithKeys("tab"),
		key.WithHelp("tab", "切换焦点"),
	),

	// 编辑器快捷键
	Send: key.NewBinding(
		key.WithKeys("enter", "ctrl+s"),
		key.WithHelp("enter", "发送消息"),
	),
	OpenEditor: key.NewBinding(
		key.WithKeys("ctrl+e"),
		key.WithHelp("ctrl+e", "打开编辑器"),
	),

	// 附件删除快捷键
	AttachmentDeleteMode: key.NewBinding(
		key.WithKeys("ctrl+r"),
		key.WithHelp("ctrl+r+{i}", "删除指定索引的附件"),
	),
	Escape: key.NewBinding(
		key.WithKeys("esc"),
		key.WithHelp("esc", "取消删除模式"),
	),
	DeleteAllAttachments: key.NewBinding(
		key.WithKeys("r"),
		key.WithHelp("ctrl+r+r", "删除所有附件"),
	),
}

// 其他按键绑定
var (
	HelpToggleKey = key.NewBinding(
		key.WithKeys("?"),
		key.WithHelp("?", "切换帮助"),
	)

	ReturnKey = key.NewBinding(
		key.WithKeys("esc"),
		key.WithHelp("esc", "关闭"),
	)

	LogsReturnKey = key.NewBinding(
		key.WithKeys("esc", "backspace", QuitKey),
		key.WithHelp("esc/q", "返回"),
	)
)

// UI文本配置
type UITexts struct {
	// 状态栏文本
	HelpText string

	// Header展开
	HeaderOpenText string

	// 错误消息
	WindowTooSmallMessage string
	AgentBusyMessage      string
	MessageEmptyWarning   string

	// 权限对话框文本
	PermissionTitle        string
	PermissionAllowText    string
	PermissionAllowSession string
	PermissionDenyText     string

	// 交互提示文本
	NavigationHint string
}

// 默认UI文本配置（英文国际化）
var DefaultUITexts = UITexts{
	HelpText: "ctrl+h help",

	HeaderOpenText: "ctrl+d open",

	WindowTooSmallMessage: "Terminal window too small. Required: %dx%d, Current: %dx%d",
	AgentBusyMessage:      "Agent is working, please wait...",
	MessageEmptyWarning:   "Message is empty",

	PermissionTitle:        "Permission Required",
	PermissionAllowText:    "Yes",
	PermissionAllowSession: "Yes, and don't ask again this session",
	PermissionDenyText:     "No, and tell me what to do differently",

	NavigationHint: "Use ↑↓ to navigate, Enter to select, or press 1-3",
}

// 格式化配置
type FormatConfig struct {
	// 令牌格式化阈值
	TokenMegaThreshold int64
	TokenKiloThreshold int64

	// 成本格式化精度
	CostDecimalPlaces int

	// 警告阈值
	TokenWarningPercentage float64
}

// 默认格式化配置
var DefaultFormatConfig = FormatConfig{
	TokenMegaThreshold:     1_000_000,
	TokenKiloThreshold:     1_000,
	CostDecimalPlaces:      2,
	TokenWarningPercentage: 80.0,
}

// TUI配置
type TuiConfig struct {
	Theme               string
	CurrentModel        models.ModelId
	AgentCoder          config.AgentName
	AutoCompact         bool
	Agents              map[config.AgentName]config.AgentConfig
	ProjectInitialized  bool
	AvailableModels     map[models.ModelId]models.Model
	Providers           map[models.ModelProvider]config.ProviderConfig
	UpdateModelCallback func(string, models.ModelId) error
	ThemeCallback       func(string) error
	InitCallback        func() error
	ShouldShowInit      func() (bool, error)
}

type projectInitializer interface {
	Initialized() bool
	Initialize() error
}

func NewTuiConfig(
	cfg *config.Config,
	tuiCfg cmdCfg.TuiConfig,
	themeUpdateCallback func(string) error,
	initializer projectInitializer) *TuiConfig {
	availableModels := make(map[models.ModelId]models.Model)
	for _, agentCfg := range cfg.Agents {
		if model, exists := models.SupportedModels[agentCfg.Model]; exists {
			availableModels[agentCfg.Model] = model
		}
	}

	tuiConfig := &TuiConfig{
		Theme:           tuiCfg.Theme,
		CurrentModel:    cfg.Agents[config.AgentCoder].Model,
		AgentCoder:      config.AgentCoder,
		AutoCompact:     cfg.AutoCompact,
		Agents:          cfg.Agents,
		AvailableModels: availableModels,
		Providers:       cfg.Providers,
		ThemeCallback:   themeUpdateCallback,
		InitCallback:    initializer.Initialize,
		ShouldShowInit: func() (bool, error) {
			return !initializer.Initialized(), nil
		},
	}

	return tuiConfig
}
