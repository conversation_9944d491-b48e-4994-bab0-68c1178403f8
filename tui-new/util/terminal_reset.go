package util

import (
	"time"

	tea "github.com/charmbracelet/bubbletea"
)

// TerminalFullResetMsg 完全重置终端消息
type TerminalFullResetMsg struct{}

// WindowResizeDebounceMsg 窗口调整防抖定时器到期消息
type WindowResizeDebounceMsg struct {
	Width  int
	Height int
}

// CreateResizeDebounceTimer 创建窗口调整防抖定时器
// 等待指定时间后发送WindowResizeDebounceMsg
func CreateResizeDebounceTimer(width, height int, delay time.Duration) tea.Cmd {
	return tea.Tick(delay, func(time.Time) tea.Msg {
		return WindowResizeDebounceMsg{
			Width:  width,
			Height: height,
		}
	})
}

// TerminalFullReset 返回完全重置终端的命令
// 这会清除屏幕、滚动缓冲区并重置终端状态
func TerminalFullReset() tea.Cmd {
	return func() tea.Msg {
		return TerminalFullResetMsg{}
	}
}

// fullResetSequence 完全重置终端的ANSI序列
// 包含以下操作：
// \033c - 完全重置终端 (Full Reset)
// \033[3J - 清除滚动缓冲区 (Clear scrollback buffer)
// \033[2J - 清除整个屏幕 (Clear entire screen)
// \033[H - 移动光标到左上角 (Move cursor to home position)
// \033[?25l - 隐藏光标 (Hide cursor)
func fullResetSequence() string {
	return "\033c\033[3J\033[2J\033[H\033[?25l"
}

// ExecuteTerminalFullReset 执行完全的终端重置
// 这是一个同步操作，直接输出ANSI序列
func ExecuteTerminalFullReset() tea.Cmd {
	return tea.Sequence(
		// 发送ANSI重置序列
		func() tea.Msg {
			// 输出完全重置序列
			print(fullResetSequence())
			return TerminalFullResetMsg{}
		},
		// 额外确保清屏
		tea.ClearScreen,
		// 确保光标隐藏（BubbleTea会管理光标显示）
		tea.HideCursor,
	)
}
