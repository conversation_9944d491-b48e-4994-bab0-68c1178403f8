package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"

	"github.com/qoder-ai/qoder-cli/core/config"
	"github.com/qoder-ai/qoder-cli/core/llm/models"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/utils"
	"github.com/qoder-ai/qoder-cli/qoder"
	"github.com/spf13/viper"
)

type TuiConfig struct {
	Theme string `json:"theme,omitempty"`
}
type Config struct {
	config.Config `mapstructure:",squash"`
	Tui           TuiConfig `json:"tui"`
}

const (
	defaultLogLevel          = "info"
	appName                  = "qoder-cli"
	MaxTokensFallbackDefault = 4096
)

var defaultContextPaths = []string{
	".github/copilot-instructions.md",
	".cursorrules",
	".cursor/rules/",
	"CLAUDE.md",
	"CLAUDE.local.md",
}

var cfg *Config

func Load(workingDir string, debug bool) (*Config, error) {
	if cfg != nil {
		return cfg, nil
	}

	cfg = &Config{
		Config: config.Config{
			AutoCompact: false,
			Shell:       config.ShellConfig{},
			McpServers:  make(map[string]config.McpServer),
			Providers:   make(map[models.ModelProvider]config.ProviderConfig),
			LspConfigs:  make(map[string]config.LspConfig),
		},
	}

	configureViper()
	setDefaults(debug)
	setProviderDefaults()

	if err := readConfig(viper.ReadInConfig()); err != nil {
		return cfg, err
	}

	mergeLocalConfig(workingDir)

	if err := viper.Unmarshal(cfg); err != nil {
		return cfg, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	applyDefaultValues()

	cfg.WorkingDir = workingDir
	err := setLogging(cfg)
	if err != nil {
		return cfg, err
	}

	if err := validate(); err != nil {
		return cfg, fmt.Errorf("config validation failed: %w", err)
	}

	return cfg, nil
}

func setLogging(cfg *Config) error {
	defaultLevel := slog.LevelInfo
	if cfg.Debug {
		defaultLevel = slog.LevelDebug
	}

	if os.Getenv("QODER_CLI_DEV_DEBUG") == "true" {
		loggingFile := fmt.Sprintf("%s/%s", utils.GetGlobalStorageDir(), "debug.log")
		messagesPath := fmt.Sprintf("%s/%s", utils.GetGlobalStorageDir(), "messages")

		if _, err := os.Stat(loggingFile); os.IsNotExist(err) {
			if err := os.MkdirAll(utils.GetGlobalStorageDir(), 0o755); err != nil {
				return fmt.Errorf("failed to create directory: %w", err)
			}
			if _, err := os.Create(loggingFile); err != nil {
				return fmt.Errorf("failed to create log file: %w", err)
			}
		}

		if _, err := os.Stat(messagesPath); os.IsNotExist(err) {
			if err := os.MkdirAll(messagesPath, 0o756); err != nil {
				return fmt.Errorf("failed to create directory: %w", err)
			}
		}
		logging.MessageDir = messagesPath

		sloggingFileWriter, err := os.OpenFile(loggingFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0o666)
		if err != nil {
			return fmt.Errorf("failed to open log file: %w", err)
		}
		logger := slog.New(slog.NewTextHandler(sloggingFileWriter, &slog.HandlerOptions{
			Level: defaultLevel,
		}))
		slog.SetDefault(logger)
	} else {
		logger := slog.New(slog.NewTextHandler(logging.NewWriter(), &slog.HandlerOptions{
			Level: defaultLevel,
		}))
		slog.SetDefault(logger)
	}
	return nil
}

func configureViper() {
	viper.SetConfigName(fmt.Sprintf(".%s", appName))
	viper.SetConfigType("json")
	viper.AddConfigPath("$HOME")
	viper.SetEnvPrefix(strings.ToUpper(appName))
	viper.AutomaticEnv()
}

func setDefaults(debug bool) {
	viper.SetDefault("contextPaths", defaultContextPaths)
	viper.SetDefault("tui.theme", "tron")
	viper.SetDefault("autoCompact", true)

	shellPath := os.Getenv("SHELL")
	if shellPath == "" {
		shellPath = "/bin/bash"
	}
	viper.SetDefault("shell.path", shellPath)
	viper.SetDefault("shell.args", []string{"-l", "-i"})

	if debug {
		viper.SetDefault("debug", true)
		viper.Set("log.level", "debug")
	} else {
		viper.SetDefault("debug", false)
		viper.SetDefault("log.level", defaultLogLevel)
	}
}

func setProviderDefaults() {
	// Idealab优先级最高
	if apiKey := os.Getenv("IDEALAB_API_KEY"); apiKey != "" {
		viper.SetDefault("agents.coder.model", models.IdeaLabSonnet4Thinking)
		viper.SetDefault("agents.reviewer.model", models.IdeaLabSonnet4Thinking)
		viper.SetDefault("agents.summarizer.model", models.IdeaLabSonnet4Thinking)
		viper.SetDefault("agents.task.model", models.IdeaLabSonnet4Thinking)
		viper.SetDefault("agents.title.model", models.IdeaLabSonnet4Thinking)
		viper.SetDefault("providers.openai.apiKey", os.Getenv("IDEALAB_API_KEY"))
		return
	}

	// 如果配了百炼，优先千问
	if apiKey := os.Getenv("DASHSCOPE_API_KEY"); apiKey != "" {
		viper.SetDefault("agents.coder.model", models.Qwen3CoderPlus)
		viper.SetDefault("agents.reviewer.model", models.Qwen3CoderPlus)
		viper.SetDefault("agents.summarizer.model", models.Qwen3CoderPlus)
		viper.SetDefault("agents.task.model", models.Qwen3CoderPlus)
		viper.SetDefault("agents.title.model", models.Qwen3CoderPlus)
		viper.SetDefault("providers.openai.apiKey", os.Getenv("DASHSCOPE_API_KEY"))
		return
	}

	// 否则用Qoder
	viper.SetDefault("agents.coder.model", models.QoderSonnet4Thinking)
	viper.SetDefault("agents.reviewer.model", models.QoderSonnet4Thinking)
	viper.SetDefault("agents.summarizer.model", models.QoderSonnet4Thinking)
	viper.SetDefault("agents.task.model", models.QoderSonnet4Thinking)
	viper.SetDefault("agents.title.model", models.QoderSonnet4Thinking)
}

func readConfig(err error) error {
	if err == nil {
		return nil
	}
	var configFileNotFoundError viper.ConfigFileNotFoundError
	if errors.As(err, &configFileNotFoundError) {
		return nil
	}

	return fmt.Errorf("failed to read config: %w", err)
}

func mergeLocalConfig(workingDir string) {
	local := viper.New()
	local.SetConfigName(fmt.Sprintf(".%s", appName))
	local.SetConfigType("json")
	local.AddConfigPath(workingDir)
	if err := local.ReadInConfig(); err == nil {
		viper.MergeConfigMap(local.AllSettings())
	}
}

func applyDefaultValues() {
	for k, v := range cfg.McpServers {
		if v.Type == "" {
			v.Type = config.McpStdio
			cfg.McpServers[k] = v
		}
	}

	cfg.ModelUpdateHook = UpdateAgentModel
}

func validateAgent(cfg *Config, name config.AgentName, agent config.AgentConfig) error {
	model, modelExists := models.SupportedModels[agent.Model]
	if !modelExists {
		logging.Warn("unsupported model configured, reverting to default", "agent", name, "configured_model", agent.Model)

		if setDefaultModelForAgent(name) {
			logging.Info("set default model for agent", "agent", name, "model", cfg.Agents[name].Model)
		} else {
			return fmt.Errorf("no valid provider available for agent %s", name)
		}
		return nil
	}

	provider := model.Provider
	providerCfg, providerExists := cfg.Providers[provider]

	if !providerExists {
		apiKey := getProviderAPIKey(provider)
		if apiKey == "" {
			logging.Warn("provider not configured for model, reverting to default", "agent", name, "model", agent.Model, "provider", provider)

			if setDefaultModelForAgent(name) {
				logging.Info("set default model for agent", "agent", name, "model", cfg.Agents[name].Model)
			} else {
				return fmt.Errorf("no valid provider available for agent %s", name)
			}
		} else {
			cfg.Providers[provider] = config.ProviderConfig{
				ApiKey: apiKey,
			}
			logging.Info("added provider from environment", "provider", provider)
		}
	} else if providerCfg.Disabled || providerCfg.ApiKey == "" {
		logging.Warn("provider is disabled or has no API key, reverting to default",
			"agent", name,
			"model", agent.Model,
			"provider", provider)

		if setDefaultModelForAgent(name) {
			logging.Info("set default model for agent", "agent", name, "model", cfg.Agents[name].Model)
		} else {
			return fmt.Errorf("no valid provider available for agent %s", name)
		}
	}

	// Validate max tokens
	if agent.MaxTokens <= 0 {
		logging.Warn("invalid max tokens, setting to default",
			"agent", name,
			"model", agent.Model,
			"max_tokens", agent.MaxTokens)

		// Update the agent with default max tokens
		updatedAgent := cfg.Agents[name]

		if name == config.AgentTitle {
			updatedAgent.MaxTokens = 80
		} else if model.DefaultMaxTokens > 0 {
			updatedAgent.MaxTokens = model.DefaultMaxTokens
		} else {
			updatedAgent.MaxTokens = MaxTokensFallbackDefault
		}
		cfg.Agents[name] = updatedAgent
	} else if model.ContextWindow > 0 && agent.MaxTokens > model.ContextWindow/2 {
		// Ensure max tokens doesn't exceed half the context window (reasonable limit)
		logging.Warn("max tokens exceeds half the context window, adjusting",
			"agent", name,
			"model", agent.Model,
			"max_tokens", agent.MaxTokens,
			"context_window", model.ContextWindow)

		// Update the agent with adjusted max tokens
		updatedAgent := cfg.Agents[name]
		updatedAgent.MaxTokens = model.ContextWindow / 2
		cfg.Agents[name] = updatedAgent
	}

	if model.CanReason && provider == models.ProviderOpenAI {
		if agent.ReasoningEffort == "" {
			// Set default reasoning effort for models that support it
			logging.Info("setting default reasoning effort for model that supports reasoning",
				"agent", name,
				"model", agent.Model)

			// Update the agent with default reasoning effort
			updatedAgent := cfg.Agents[name]
			updatedAgent.ReasoningEffort = "medium"
			cfg.Agents[name] = updatedAgent
		} else {
			effort := strings.ToLower(agent.ReasoningEffort)
			if effort != "low" && effort != "medium" && effort != "high" {
				logging.Warn("invalid reasoning effort, setting to medium",
					"agent", name,
					"model", agent.Model,
					"reasoning_effort", agent.ReasoningEffort)

				updatedAgent := cfg.Agents[name]
				updatedAgent.ReasoningEffort = "medium"
				cfg.Agents[name] = updatedAgent
			}
		}
	} else if !model.CanReason && agent.ReasoningEffort != "" {
		// Model doesn't support reasoning but reasoning effort is set
		logging.Warn("model doesn't support reasoning but reasoning effort is set, ignoring",
			"agent", name,
			"model", agent.Model,
			"reasoning_effort", agent.ReasoningEffort)

		// Update the agent to remove reasoning effort
		updatedAgent := cfg.Agents[name]
		updatedAgent.ReasoningEffort = ""
		cfg.Agents[name] = updatedAgent
	}

	return nil
}

func validate() error {
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	for name, agent := range cfg.Agents {
		if err := validateAgent(cfg, name, agent); err != nil {
			return err
		}
	}

	for provider, providerCfg := range cfg.Providers {
		if providerCfg.ApiKey == "" && !providerCfg.Disabled {
			fmt.Printf("provider has no API key, marking as disabled %s", provider)
			logging.Warn("provider has no API key, marking as disabled", "provider", provider)
			providerCfg.Disabled = true
			cfg.Providers[provider] = providerCfg
		}
	}

	for language, lspConfig := range cfg.LspConfigs {
		if lspConfig.Command == "" && lspConfig.Enabled {
			logging.Warn("LSP configuration has no command, marking as disabled", "language", language)
			lspConfig.Enabled = false
			cfg.LspConfigs[language] = lspConfig
		}
	}

	return nil
}

func getProviderAPIKey(provider models.ModelProvider) string {
	switch provider {
	case models.ProviderQoder:
		if qoder.GetCachedUserInfo() == nil {
			return ""
		}
		return qoder.GetCachedUserInfo().Name
	case models.ProviderIdeaLab:
		return os.Getenv("IDEALAB_API_KEY")
	case models.ProviderDashScope:
		return os.Getenv("DASHSCOPE_API_KEY")
	}
	return ""
}

func setDefaultModelForAgent(agent config.AgentName) bool {
	if os.Getenv("IDEALAB_API_KEY") != "" {
		maxTokens := int64(200000)
		if agent == config.AgentTitle {
			maxTokens = 80
		}

		cfg.Agents[agent] = config.AgentConfig{
			Model:     models.IdeaLabSonnet4Thinking,
			MaxTokens: maxTokens,
		}

		return true
	}

	if os.Getenv("DASHSCOPE_API_KEY") != "" {
		maxTokens := int64(200000)
		if agent == config.AgentTitle {
			maxTokens = 80
		}

		cfg.Agents[agent] = config.AgentConfig{
			Model:     models.Qwen3CoderPlus,
			MaxTokens: maxTokens,
		}

		return true
	}

	if qoder.GetCachedUserInfo() == nil || qoder.GetCachedUserInfo().Name == "" {
		return false
	}

	maxTokens := int64(200000)
	if agent == config.AgentTitle {
		maxTokens = 80
	}

	cfg.Agents[agent] = config.AgentConfig{
		Model:     models.QoderSonnet4Thinking,
		MaxTokens: maxTokens,
	}
	return true
}

func updateCfgFile(updateCfg func(config *Config)) error {
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	// Get the config file path
	configFile := viper.ConfigFileUsed()
	var configData []byte
	if configFile == "" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return fmt.Errorf("failed to get home directory: %w", err)
		}
		configFile = filepath.Join(homeDir, fmt.Sprintf(".%s.json", appName))
		logging.Info("config file not found, creating new one", "path", configFile)
		configData = []byte(`{}`)
	} else {
		// Read the existing config file
		data, err := os.ReadFile(configFile)
		if err != nil {
			return fmt.Errorf("failed to read config file: %w", err)
		}
		configData = data
	}

	// Parse the JSON
	var userCfg *Config
	if err := json.Unmarshal(configData, &userCfg); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	updateCfg(userCfg)

	updatedData, err := json.MarshalIndent(userCfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(configFile, updatedData, 0o644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

func UpdateAgentModel(agentName config.AgentName, modelID models.ModelId) error {
	if cfg == nil {
		panic("config not loaded")
	}

	existingAgentCfg := cfg.Agents[agentName]

	model, ok := models.SupportedModels[modelID]
	if !ok {
		return fmt.Errorf("model %s not supported", modelID)
	}

	maxTokens := existingAgentCfg.MaxTokens
	if model.DefaultMaxTokens > 0 {
		maxTokens = model.DefaultMaxTokens
	}

	newAgentCfg := config.AgentConfig{
		Model:           modelID,
		MaxTokens:       maxTokens,
		ReasoningEffort: existingAgentCfg.ReasoningEffort,
	}
	cfg.Agents[agentName] = newAgentCfg

	if err := validateAgent(cfg, agentName, newAgentCfg); err != nil {
		cfg.Agents[agentName] = existingAgentCfg
		return fmt.Errorf("failed to update agent model: %w", err)
	}

	return updateCfgFile(func(cfg *Config) {
		if cfg.Agents == nil {
			cfg.Agents = make(map[config.AgentName]config.AgentConfig)
		}
		cfg.Agents[agentName] = newAgentCfg
	})
}

func UpdateTheme(themeName string) error {
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	cfg.Tui.Theme = themeName
	return updateCfgFile(func(config *Config) {
		config.Tui.Theme = themeName
	})
}
