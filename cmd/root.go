package cmd

import (
	"context"
	"fmt"
	"github.com/qoder-ai/qoder-cli/cmd/config"
	"github.com/qoder-ai/qoder-cli/core/logging"
	"github.com/qoder-ai/qoder-cli/core/pubsub"
	"github.com/qoder-ai/qoder-cli/core/runtime"
	"github.com/qoder-ai/qoder-cli/core/version"
	"github.com/qoder-ai/qoder-cli/tui"
	"github.com/qoder-ai/qoder-cli/tui-new"
	tuinewconfig "github.com/qoder-ai/qoder-cli/tui-new/config"
	themenew "github.com/qoder-ai/qoder-cli/tui-new/theme"
	tuiconfig "github.com/qoder-ai/qoder-cli/tui/config"
	"github.com/qoder-ai/qoder-cli/tui/theme"
	"os"
	"sync"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	zone "github.com/lrstanley/bubblezone"
	"github.com/spf13/cobra"
)

var application *runtime.AppRuntime
var cfg *config.Config

func validateCommandFlags(cmd *cobra.Command) error {
	systemPrompt, _ := cmd.Flags().GetString("system-prompt")
	prompt, _ := cmd.Flags().GetString("prompt")

	if systemPrompt != "" && prompt == "" {
		return fmt.Errorf("--system-prompt can only be used with --prompt (-p)")
	}
	return nil
}

var rootCmd = &cobra.Command{
	Use:   "qoder-cli",
	Short: "Terminal-based AI assistant for software development",
	Long: `Qoder CLI is a powerful terminal-based AI assistant that helps with software development tasks.
It provides an interactive chat interface with AI capabilities, code analysis, and LSP integration
to assist developers in writing, debugging, and understanding code directly from the terminal.`,
	Example: `
  # Run in interactive mode
  qoder-cli

  # Run with debug logging
  qoder-cli -d

  # Run with debug logging in a specific directory
  qoder-cli -d -c /path/to/project

  # Print version
  qoder-cli -v

  # Run a single non-interactive prompt
  qoder-cli -p "Explain the use of context in Go"

  # Run a single non-interactive prompt with JSON output format
  qoder-cli -p "Explain the use of context in Go" -f json
  `,
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		if err := validateCommandFlags(cmd); err != nil {
			return err
		}

		debug, _ := cmd.Flags().GetBool("debug")
		cwd, _ := cmd.Flags().GetString("cwd")

		if cwd != "" {
			err := os.Chdir(cwd)
			if err != nil {
				return fmt.Errorf("failed to change directory: %v", err)
			}
		}
		if cwd == "" {
			c, err := os.Getwd()
			if err != nil {
				return fmt.Errorf("failed to get current working directory: %v", err)
			}
			cwd = c
		}
		var err error
		cfg, err = config.Load(cwd, debug)
		if err != nil {
			return err
		}

		systemPrompt, _ := cmd.Flags().GetString("system-prompt")
		if systemPrompt != "" {
			cfg.CustomSystemPrompt = &systemPrompt
		}

		app, err := runtime.NewAppRuntime(cmd.Context(), &cfg.Config)
		if err != nil {
			logging.Error("Failed to create app: %v", err)
			return err
		}
		application = app
		return nil
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		if cmd.Flag("version").Changed {
			fmt.Println(version.Version)
			return nil
		}

		prompt, _ := cmd.Flags().GetString("prompt")
		sessionId, _ := cmd.Flags().GetString("resume")
		outputFormat, _ := cmd.Flags().GetString("output-format")

		ctx := cmd.Context()

		if prompt != "" {
			return runNonInteractiveMode(ctx, prompt, sessionId, outputFormat)
		}

		return runInteractiveMode(ctx, application, cfg)
	},
	PersistentPostRunE: func(cmd *cobra.Command, args []string) error {
		application.Shutdown()
		return nil
	},
}

func runNonInteractiveMode(ctx context.Context, prompt, sessionId, outputFormat string) error {
	ch, cancelSubs := setupSubscriptions(application, ctx)

	var outputWg sync.WaitGroup
	outputWg.Add(1)
	go func() {
		defer outputWg.Done()
		outputNonInteractiveStream(ch, OutputFormat(outputFormat))
	}()

	err := application.RunNonInteractive(ctx, prompt, sessionId)

	cancelSubs()
	outputWg.Wait()

	return err
}

// runInteractiveMode handles the interactive TUI mode
func runInteractiveMode(
	ctx context.Context,
	app *runtime.AppRuntime,
	cfg *config.Config) error {
	zone.NewGlobal()

	var program *tea.Program

	wd, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get current working directory: %v", err)
	}

	if os.Getenv("UI") == "new" {
		tuiConfig := tuinewconfig.NewTuiConfig(&cfg.Config, cfg.Tui, config.UpdateTheme, app.GetProjectService())
		themenew.InitTheme(tuiConfig)
		program = tea.NewProgram(tui_new.New(app, app.GetPermissionService(), tuiConfig))
	} else {
		tuiConfig := tuiconfig.NewTuiConfig(&cfg.Config, cfg.Tui, config.UpdateTheme, app.GetProjectService(), wd)
		theme.InitTheme(tuiConfig)
		program = tea.NewProgram(
			tui.New(app, app.GetPermissionService(), tuiConfig),
			tea.WithAltScreen(),
			tea.WithMouseCellMotion(),
		)
	}

	ch, cancelSubs := setupSubscriptions(app, ctx)
	tuiCtx, tuiCancel := context.WithCancel(ctx)
	var tuiWg sync.WaitGroup
	tuiWg.Add(1)

	go func() {
		defer tuiWg.Done()
		defer logging.RecoverPanic("TUI-message-handler", func() {
			attemptTUIRecovery(program)
		})

		for {
			select {
			case <-tuiCtx.Done():
				logging.Info("TUI message handler shutting down")
				return
			case msg, ok := <-ch:
				if !ok {
					logging.Info("TUI message channel closed")
					return
				}
				program.Send(msg)
			}
		}
	}()

	cleanup := func() {
		app.Shutdown()
		cancelSubs()
		tuiCancel()
		tuiWg.Wait()

		logging.Info("All goroutines cleaned up")
	}

	result, err := program.Run()
	cleanup()

	if err != nil {
		logging.Error("TUI error: %v", err)
		return fmt.Errorf("TUI error: %v", err)
	}

	logging.Info("TUI exited with result: %v", result)
	return nil
}

func attemptTUIRecovery(program *tea.Program) {
	logging.Info("Attempting to recover TUI after panic")
	program.Quit()
}

func setupSubscriber[T any](
	ctx context.Context,
	wg *sync.WaitGroup,
	name string,
	subscriber func(context.Context) <-chan pubsub.Event[T],
	outputCh chan<- tea.Msg,
) {
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer logging.RecoverPanic(fmt.Sprintf("subscription-%s", name), nil)

		subCh := subscriber(ctx)

		for {
			select {
			case event, ok := <-subCh:
				if !ok {
					logging.Info("subscription channel closed", "name", name)
					return
				}

				var msg tea.Msg = event

				select {
				case outputCh <- msg:
				case <-time.After(2 * time.Second):
					logging.Warn("message dropped due to slow consumer", "name", name)
				case <-ctx.Done():
					logging.Info("subscription cancelled", "name", name)
					return
				}
			case <-ctx.Done():
				logging.Info("subscription cancelled", "name", name)
				return
			}
		}
	}()
}

func setupSubscriptions(app *runtime.AppRuntime, parentCtx context.Context) (chan tea.Msg, func()) {
	ch := make(chan tea.Msg, 100)

	wg := sync.WaitGroup{}
	ctx, cancel := context.WithCancel(parentCtx)
	setupSubscriber(ctx, &wg, "logging", logging.Subscribe, ch)
	setupSubscriber(ctx, &wg, "sessions", app.GetSessionService().Subscribe, ch)
	setupSubscriber(ctx, &wg, "messages", app.GetMessageService().Subscribe, ch)
	setupSubscriber(ctx, &wg, "permissions", app.GetPermissionService().Subscribe, ch)
	setupSubscriber(ctx, &wg, "coderAgent", app.GetCoderAgent().Subscribe, ch)
	setupSubscriber(ctx, &wg, "reviewerAgent", app.GetReviewerAgent().Subscribe, ch)

	cleanupFunc := func() {
		logging.Info("Cancelling all subscriptions")
		cancel()

		waitCh := make(chan struct{})
		go func() {
			defer logging.RecoverPanic("subscription-cleanup", nil)
			wg.Wait()
			close(waitCh)
		}()

		select {
		case <-waitCh:
			logging.Info("All subscription goroutines completed successfully")
			close(ch)
		case <-time.After(5 * time.Second):
			logging.Warn("Timed out waiting for some subscription goroutines to complete")
			close(ch)
		}
	}
	return ch, cleanupFunc
}

func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	rootCmd.PersistentFlags().BoolP("version", "v", false, "Version")
	rootCmd.PersistentFlags().BoolP("debug", "d", false, "Debug")
	rootCmd.PersistentFlags().StringP("cwd", "c", "", "Current working directory")
	rootCmd.Flags().StringP("resume", "r", "", "Resume a conversation - provide a session Id to resume")
	rootCmd.Flags().StringP("prompt", "p", "", "Prompt to run in non-interactive mode")
	rootCmd.Flags().String("system-prompt", "", "Custom system prompt (only works with --prompt)")
	rootCmd.Flags().StringP("output-format", "f", Text.String(),
		"Output format for non-interactive mode (text, json, stream-json)")
	rootCmd.Flags().BoolP("quiet", "q", false, "Hide spinner in non-interactive mode")
	_ = rootCmd.RegisterFlagCompletionFunc("output-format", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return SupportedFormats, cobra.ShellCompDirectiveNoFileComp
	})

	// Add server subcommand
	rootCmd.AddCommand(serverCmd)
}
