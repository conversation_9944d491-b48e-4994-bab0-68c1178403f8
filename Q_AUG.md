# QoderCLI 系统架构分析

## 项目概述

QoderCLI 是一个基于 Go 语言开发的 AI 代码助手工具，类似于 Claude Code 的复刻版本。该项目集成了大语言模型（LLM）、语言服务器协议（LSP）、终端用户界面（TUI）等多个组件，为开发者提供智能的代码编辑和分析功能。

## 整体架构图

```mermaid
graph TB
    subgraph "用户界面层"
        TUI[TUI 终端界面]
        TUI_NEW[TUI-NEW 新版界面]
        CMD[命令行接口]
    end
    
    subgraph "核心应用层"
        APP[App Runtime]
        CONFIG[配置管理]
        SESSION[会话管理]
        MESSAGE[消息管理]
        HISTORY[历史管理]
        PROJECT[项目管理]
        PERMISSION[权限管理]
    end
    
    subgraph "AI 智能层"
        AGENT_CODER[Coder Agent]
        AGENT_REVIEWER[Reviewer Agent]
        AGENT_TASK[Task Agent]
        PROVIDER[LLM Provider]
        TOOLS[工具系统]
    end
    
    subgraph "语言服务层"
        LSP_CLIENT[LSP 客户端]
        LSP_PROTOCOL[LSP 协议]
        LSP_HANDLERS[LSP 处理器]
    end
    
    subgraph "外部服务"
        LLM_API[大语言模型 API]
        LSP_SERVERS[LSP 服务器]
        MCP_SERVERS[MCP 服务器]
        FILE_SYSTEM[文件系统]
    end
    
    TUI --> APP
    TUI_NEW --> APP
    CMD --> APP
    
    APP --> AGENT_CODER
    APP --> AGENT_REVIEWER
    APP --> SESSION
    APP --> MESSAGE
    APP --> HISTORY
    APP --> PROJECT
    APP --> PERMISSION
    
    AGENT_CODER --> PROVIDER
    AGENT_REVIEWER --> PROVIDER
    AGENT_TASK --> PROVIDER
    AGENT_CODER --> TOOLS
    AGENT_REVIEWER --> TOOLS
    
    PROVIDER --> LLM_API
    TOOLS --> FILE_SYSTEM
    TOOLS --> MCP_SERVERS
    
    APP --> LSP_CLIENT
    LSP_CLIENT --> LSP_SERVERS
    LSP_CLIENT --> LSP_PROTOCOL
    LSP_CLIENT --> LSP_HANDLERS
```

## 核心模块详解

### 1. 应用运行时 (Runtime)

**位置**: `core/runtime/runtime.go`

应用运行时是整个系统的核心协调器，负责：
- 初始化和管理所有核心服务
- 创建和配置 AI Agent
- 管理 LSP 客户端
- 提供统一的服务接口

```go
type AppRuntime struct {
    *lspManager
    config        *config.Config
    sessions      core.SessionService
    messages      coreMsg.MessageService
    histories     core.HistoryService
    projects      core.ProjectService
    providers     coreProv.Service
    permissions   core.PermissionService
    coderAgent    coreAgent.Agent
    reviewerAgent coreAgent.Agent
}
```

### 2. 配置管理 (Config)

**位置**: `core/config/config.go`

配置系统支持多种配置项：
- **Agent 配置**: 不同 Agent 的模型、Token 限制等
- **Provider 配置**: 各种 LLM 提供商的 API 配置
- **LSP 配置**: 语言服务器的配置
- **MCP 配置**: Model Context Protocol 服务器配置
- **Shell 配置**: 命令执行环境配置

### 3. AI Agent 系统

#### Agent 类型
- **Coder Agent**: 主要的代码编写和修改 Agent
- **Reviewer Agent**: 代码审查 Agent  
- **Task Agent**: 执行特定任务的子 Agent
- **Summarizer Agent**: 内容总结 Agent
- **Title Agent**: 生成标题的 Agent

#### Agent 工具系统

**位置**: `core/internal/llm/agent/tools.go`

每个 Agent 都配备了不同的工具集：

**Coder Agent 工具集**:
- `BashTool`: 执行 Shell 命令
- `WebFetchTool`: 获取网页内容
- `EditTool`: 编辑单个文件
- `MultiEditTool`: 批量编辑多个文件
- `GlobTool`: 文件模式匹配
- `GrepTool`: 文本搜索
- `LsTool`: 列出目录内容
- `ReadTool`: 读取文件内容
- `WriteTool`: 写入文件
- `TodoWriteTool`: 创建 TODO 项
- `ExitPlanModeTool`: 退出计划模式
- `TaskTool`: 创建子任务

**Reviewer Agent 工具集**:
- `WebFetchTool`: 获取参考资料
- `ReadTool`: 读取代码文件
- `LsTool`: 浏览项目结构
- `GlobTool`: 查找相关文件
- `GrepTool`: 搜索代码模式
- `BashTool`: 执行测试命令

### 4. 消息和会话管理

#### 会话管理 (Session)
**位置**: `core/session.go`

```go
type Session struct {
    Id               string   `json:"id"`
    ParentSessionId  string   `json:"parent_session_id"`
    Title            string   `json:"title"`
    MessageCount     int64    `json:"message_count"`
    PromptTokens     int64    `json:"prompt_tokens"`
    CompletionTokens int64    `json:"completion_tokens"`
    SummaryMessageId string   `json:"summary_message_id"`
    Cost             float64  `json:"cost"`
    CreatedAt        int64    `json:"created_at"`
    UpdatedAt        int64    `json:"updated_at"`
    WorkingDir       string   `json:"working_dir"`
    ContextPaths     []string `json:"context_paths,omitempty"`
}
```

#### 消息管理 (Message)
**位置**: `core/message/message.go`

支持多种消息类型：
- 用户消息 (User)
- 助手消息 (Assistant)  
- 工具消息 (Tool)
- 系统消息 (System)

### 5. LLM Provider 系统

**位置**: `core/llm/provider/`

支持多个 LLM 提供商：
- **IdeaLab**: 内部 LLM 服务
- **Qoder**: 专用的 Qoder 模型服务
- **DashScope**: 阿里云的 LLM 服务

每个 Provider 都实现统一的接口：
```go
type Client interface {
    SendMessages(ctx context.Context, messages []message.Message, tools []tools.BaseTool) (*Response, error)
    StreamResponse(ctx context.Context, messages []message.Message, tools []tools.BaseTool) <-chan Event
    GetModel() models.Model
}
```

### 6. LSP (Language Server Protocol) 支持

**位置**: `lsp/`

LSP 系统提供：
- **多语言支持**: 支持 Go、Python、JavaScript、TypeScript 等多种编程语言
- **实时诊断**: 语法错误、类型检查等
- **代码补全**: 智能代码提示
- **跳转功能**: 定义跳转、引用查找等
- **重构支持**: 重命名、提取方法等

#### LSP 客户端架构
```go
type Client struct {
    Cmd    *exec.Cmd
    stdin  io.WriteCloser
    stdout *bufio.Reader
    stderr io.ReadCloser
    
    // 请求处理
    handlers   map[int32]chan *Message
    
    // 服务器请求处理器
    serverRequestHandlers map[string]ServerRequestHandler
    
    // 通知处理器
    notificationHandlers map[string]NotificationHandler
    
    // 诊断缓存
    diagnostics   map[protocol.DocumentUri][]protocol.Diagnostic
    
    // 打开的文件
    openFiles   map[string]*OpenFileInfo
}
```

### 7. TUI (Terminal User Interface) 系统

**位置**: `tui/` 和 `tui-new/`

项目包含两套 TUI 实现：
- **传统 TUI**: 功能完整的多页面界面
- **新版 TUI**: 简化的单页面聊天界面

#### TUI 组件架构
- **Chat 组件**: 消息显示和输入
- **Status 组件**: 状态栏显示
- **Dialog 组件**: 各种对话框
- **Layout 组件**: 布局管理
- **Theme 组件**: 主题和样式

### 8. 发布订阅系统 (PubSub)

**位置**: `core/pubsub/`

实现了类型安全的发布订阅模式：
```go
type Broker[T any] struct {
    subs      map[chan Event[T]]struct{}
    mu        sync.RWMutex
    done      chan struct{}
    subCount  int
    maxEvents int
}
```

支持的事件类型：
- `CreatedEvent`: 创建事件
- `UpdatedEvent`: 更新事件  
- `DeletedEvent`: 删除事件

## 数据流图

```mermaid
sequenceDiagram
    participant User as 用户
    participant TUI as TUI界面
    participant App as App Runtime
    participant Agent as Coder Agent
    participant Provider as LLM Provider
    participant Tools as 工具系统
    participant LSP as LSP客户端
    
    User->>TUI: 输入代码请求
    TUI->>App: 创建会话和消息
    App->>Agent: 启动Agent处理
    Agent->>Provider: 发送消息到LLM
    Provider-->>Agent: 返回响应和工具调用
    Agent->>Tools: 执行工具调用
    Tools->>LSP: 获取代码信息
    LSP-->>Tools: 返回诊断信息
    Tools-->>Agent: 返回工具结果
    Agent->>Provider: 发送工具结果
    Provider-->>Agent: 返回最终响应
    Agent-->>App: 完成处理
    App-->>TUI: 更新界面
    TUI-->>User: 显示结果
```

## 关键特性

### 1. 模块化设计
- 清晰的分层架构
- 接口驱动的设计
- 可插拔的组件系统

### 2. 并发安全
- 使用 Go 的 goroutine 和 channel
- 适当的锁机制
- 优雅的关闭处理

### 3. 可扩展性
- 支持多种 LLM 提供商
- 可配置的工具系统
- 插件化的 MCP 支持

### 4. 用户体验
- 实时的代码分析
- 智能的权限管理
- 丰富的交互界面

## 配置示例

系统支持灵活的配置，例如：

```json
{
  "workingDir": "/path/to/project",
  "contextPaths": ["src/", "docs/"],
  "providers": {
    "qoder": {
      "apiKey": "your-api-key",
      "baseUrl": "https://api.qoder.ai"
    }
  },
  "agents": {
    "coder": {
      "model": "idealab_claude_sonnet4",
      "maxTokens": 50000
    }
  },
  "lspConfigs": {
    "go": {
      "command": "gopls",
      "args": ["serve"]
    }
  }
}
```

这个架构设计使得 QoderCLI 能够提供强大而灵活的 AI 辅助编程体验，同时保持良好的可维护性和可扩展性。
