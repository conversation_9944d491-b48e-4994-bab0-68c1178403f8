package qoder

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"io/fs"
	"log/slog"
	"net"
	"os"
	"path/filepath"
	"strings"
)

var machineIp = ""

type CachedUserInfo struct {
	Aid                string `json:"aid"`                  // 登录用户所属账号ID
	Uid                string `json:"uid"`                  // 登录用户ID
	Name               string `json:"name"`                 // 登录用户名
	YxUid              string `json:"yxUid"`                // 云效uid
	OrgId              string `json:"orgId"`                // 所属企业ID
	OrgName            string `json:"orgName"`              // 所属企业名称
	StaffId            string `json:"staffId"`              //工号id，企业版才有
	AvatarUrl          string `json:"avatarUrl"`            //企业头像url
	Info               string `json:"info"`                 // AES加密过的鉴权信息
	Key                string `json:"key"`                  // RSA加密过的鉴权信息秘钥
	Token              string `json:"token"`                // OAuth令牌
	SecurityOauthToken string `json:"security_oauth_token"` // 个人凭证
	RefreshToken       string `json:"refresh_token"`        // 刷新token
	TokenExpireTime    int64  `json:"expire_time"`          // token过期时间
	UserSourceChannel  string `json:"user_source_channel"`  // 用户来源，公有云/fc/pai平台等等
	// 用户类型 由用户在登陆界面选择
	// 个人标准版  personal_standard
	// （未实现）个人专业版  personal_professional
	// 企业标准版  enterprise_standard
	// 企业专业版  enterprise_professional
	UserType         string `json:"user_type"`
	DataPolicyAgreed bool   `json:"data_policy_agreed"` // 数据回流协议签署状态， 详见definition/datapolicy.go
	Email            string `json:"email"`
}

type SaveUserInfoData struct {
	Name               string `json:"name"`              // 登录显示名（主账号固定为root）
	Aid                string `json:"aid"`               // 主账号ID
	Uid                string `json:"uid"`               // 登录账号ID（主账号或子账号ID）
	YxUid              string `json:"yx_uid"`            // 云效uid
	OrgId              string `json:"organization_id"`   // 所属企业ID
	OrgName            string `json:"organization_name"` // 所属企业名称
	StaffId            string `json:"staffId"`           //工号id，企业版才有
	AvatarUrl          string `json:"avatar_url"`        //企业头像url
	LoginTimestamp     int64  `json:"login_timestamp"`   // 最后登录时间
	LoginDeviceId      string `json:"login_device_id"`   // 最后登录设备ID
	AccessToken        string `json:"access_token"`
	SecurityOauthToken string `json:"security_oauth_token"` // 个人凭证
	RefreshToken       string `json:"refresh_token"`        // 刷新token
	TokenExpireTime    int64  `json:"expire_time"`          // token过期时间
	Key                string `json:"key"`
	EncryptUserInfo    string `json:"encrypt_user_info"`
	OnPremise          string `json:"on_premise"`          //是否是专有云企业版，true/false
	UserSourceChannel  string `json:"user_source_channel"` // 用户来源，公有云/fc/pai平台等等
	// 用户类型 由用户在登陆界面选择
	// 个人标准版  personal_standard
	// （未实现）个人专业版  personal_professional
	// 企业标准版  enterprise_standard
	// 企业专业版  enterprise_professional
	UserType         string `json:"user_type"`
	DataPolicyAgreed bool   `json:"data_policy_agreed"` // 数据回流协议签署状态， 详见definition/datapolicy.go
	Email            string `json:"email"`
}

type ConfigData struct {
	BeamSearchConfig struct {
		BeamSize       int     `json:"beam_size"`
		Topk           int     `json:"topk"`
		TokenThreshold float64 `json:"token_threshold"`
		MaxLength      int     `json:"max_length"`
	} `json:"beam_search_config"`
	Env          string `json:"env"`
	GlobalConfig struct {
		AskModeUseTools       string `json:"ask_mode_use_tools"`
		CommandAllowList      string `json:"command_allow_list"`
		CommandDenyList       string `json:"command_deny_list"`
		Endpoint              string `json:"endpoint"`
		HttpProxy             string `json:"http_proxy"`
		McpAutoRun            string `json:"mcp_auto_run"`
		PreferredLanguage     string `json:"preferred_language"`
		ProxyMode             string `json:"proxy_mode"`
		QuestModeEnable       string `json:"quest_mode_enable"`
		TerminalRunMode       string `json:"terminal_run_mode"`
		WebToolsExecutionMode string `json:"web_tools_execution_mode"`
	} `json:"global_config"`
	RegionConfig struct {
		PreferredCenterNode struct {
			Endpoint string `json:"endpoint"`
			Latency  int    `json:"latency"`
		} `json:"preferredCenterNode"`
		PreferredDataNodeMap struct {
			Codebase struct {
				Region   string `json:"region"`
				Endpoint string `json:"endpoint"`
				Latency  int    `json:"latency"`
			} `json:"codebase"`
		} `json:"preferredDataNodeMap"`
		PreferredInferenceNode struct {
			Endpoint string `json:"endpoint"`
			Latency  int    `json:"latency"`
		} `json:"preferredInferenceNode"`
	} `json:"region_config"`
}

func GetCacheDir() string {
	cacheDir, _ := os.UserConfigDir()
	return filepath.Join(cacheDir, "Qoder", "SharedClientCache", "cache")
}

func GetMachineIdFile() string {
	return filepath.Join(GetCacheDir(), "id")
}

func GetUserFile() string {
	return filepath.Join(GetCacheDir(), "user")
}

func GetConfigFile() string {
	return filepath.Join(GetCacheDir(), "config.json")
}

func parseCachedUserContent(content string) *CachedUserInfo {
	if content == "" {
		return nil
	}
	var saveUserInfoData = &SaveUserInfoData{}
	if err := json.Unmarshal([]byte(content), saveUserInfoData); err != nil {
		panic("failed to parse user cache content: " + err.Error())
	}
	//新版json格式
	return &CachedUserInfo{
		Aid:                saveUserInfoData.Aid,
		Uid:                saveUserInfoData.Uid,
		Name:               saveUserInfoData.Name,
		Token:              saveUserInfoData.AccessToken,
		Key:                saveUserInfoData.Key,
		Info:               saveUserInfoData.EncryptUserInfo,
		OrgId:              saveUserInfoData.OrgId,
		OrgName:            saveUserInfoData.OrgName,
		StaffId:            saveUserInfoData.StaffId,
		YxUid:              saveUserInfoData.YxUid,
		AvatarUrl:          saveUserInfoData.AvatarUrl,
		SecurityOauthToken: saveUserInfoData.SecurityOauthToken,
		RefreshToken:       saveUserInfoData.RefreshToken,
		TokenExpireTime:    saveUserInfoData.TokenExpireTime,
		UserType:           saveUserInfoData.UserType,
	}
}

func GetCachedUserInfo() *CachedUserInfo {
	var encryptedContent []byte

	if os.Getenv("QODER_USER_INFO") != "" {
		encryptedContent = []byte(os.Getenv("QODER_USER_INFO"))
	} else {
		userFilePath := GetUserFile()
		var err error
		encryptedContent, err = os.ReadFile(userFilePath)
		if err != nil {
			// 检查错误是否为文件不存在
			if errors.Is(err, fs.ErrNotExist) {
				// 通常是因为用户未登录，没有生成user文件
				slog.Error("user info not exist, not login.")
				return nil
			}
			slog.Error(fmt.Sprintf("Failed to read user info: %s", err))
			return nil
		}
	}

	if string(encryptedContent) == "" {
		return nil
	}

	machineKey := GetMachineId()
	if string(machineKey) == "" {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			slog.Error("Recovered from user info decrypt panic. Err: %+v", r)
		}
	}()

	machineKey = machineKey[:16]

	content, err := AesDecryptWithBase64(string(encryptedContent), machineKey)
	if err != nil {
		slog.Error(fmt.Sprintf("Unable to decrypt user info. Using machineKey: %s, Err: %+v", machineKey, err))
		return nil
	}
	userInfo := parseCachedUserContent(content)

	return userInfo
}

func GetMachineIp() string {
	if machineIp == "" {
		initMachineIp()
	}
	return machineIp
}

func GetMachineId() string {
	// for tb
	if os.Getenv("QODER_MACHINE_ID") != "" {
		return os.Getenv("QODER_MACHINE_ID")
	}

	machineKey, err := os.ReadFile(GetMachineIdFile())
	if err != nil {
		// 检查错误是否为文件不存在
		if errors.Is(err, fs.ErrNotExist) {
			// 通常是因为用户未登录，没有生成user文件
			slog.Error("user info not exist, not login.")
			return ""
		}
		slog.Error(fmt.Sprintf("Failed to read machineId : %s", err))
		return ""
	}
	return strings.TrimSpace(string(machineKey))
}

func GetConfig() *ConfigData {
	content, err := os.ReadFile(GetConfigFile())
	if err != nil {
		return nil
	}

	cfg := ConfigData{}
	err = json.Unmarshal(content, &cfg)
	if err != nil {
		return nil
	}

	return &cfg
}

func initMachineIp() {
	if machineIp != "" {
		return
	}

	// 获取所有网络接口地址
	interfaceAddresses, err := net.InterfaceAddrs()
	if err != nil {
		machineIp = ""
	}
	// 遍历并打印IPv4地址（如果需要IPv6，可以适当修改条件）
	for _, address := range interfaceAddresses {
		// 检查地址是否为IPv4类型
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() && ipnet.IP.To4() != nil {
			validIp := ipnet.IP.String()
			slog.Info("Init local Machine Ip. ip=" + validIp)
			machineIp = validIp

			break
		}
	}
}

func AuthToken(url, body, timestamp string) (string, string) {
	userInfo := GetCachedUserInfo()
	if userInfo == nil {
		return "", ""
	}
	payload := getAuthPayload(userInfo.Info, "")
	signature := getAuthSignature(payload, url, body, timestamp, userInfo.Key)

	return fmt.Sprintf("Bearer COSY.%s.%s", payload, signature), userInfo.Key
}

func GetUserIdAndName() (string, string, string) {
	if userInfo := GetCachedUserInfo(); userInfo != nil {
		return userInfo.Aid, userInfo.Uid, userInfo.Name
	}
	return "", "", ""
}

func getAuthSignature(payload, url, body, timestamp, key string) string {
	return Md5Encode(fmt.Sprintf("%s\n%s\n%s\n%s\n%s",
		payload, key, timestamp, body, removeQueryParameters(url)))
}

func removeQueryParameters(url string) string {
	if index := strings.Index(url, "?"); index > 0 {
		return url[:index]
	}
	return url
}

func getAuthPayload(userInfo string, ideVersion string) string {
	data := map[string]string{
		"version":     "v1",
		"requestId":   uuid.NewString(),
		"info":        userInfo,
		"cosyVersion": "dev",
		"ideVersion":  ideVersion,
		//"dataPolicySignStatus": config.DataPolicyAgreed,
	}
	payload, _ := json.Marshal(data)
	return Base64Encode(payload)
}
